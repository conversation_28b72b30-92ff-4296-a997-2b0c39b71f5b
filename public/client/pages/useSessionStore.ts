// stores/sessionStore.js
import {defineStore} from 'pinia';

export const useSessionStore = defineStore('session', {
	state: () => ({
		loggedIn: false,
		sessionInterval: null,
	}),
	actions: {
		checkSession() {
			const token = localStorage.getItem('token');
			console.log('checking session', token);
			if (token) {
				const tokenData = JSON.parse(atob(token.split('.')[1]));
				const exp = tokenData.exp;
				if (exp * 1000 < Date.now()) {
					localStorage.removeItem('token');
					this.loggedIn = false;
					console.log('Session expired');
				} else {
					this.loggedIn = true;
				}
			} else {
				this.loggedIn = false;
			}
		},
		startSessionCheck() {
			this.sessionInterval = setInterval(() => {
				this.checkSession();
			}, 300000);
		},
		stopSessionCheck() {
			clearInterval(this.sessionInterval);
		},
		logIn() {
			this.loggedIn = true;
			this.stopSessionCheck();
			this.startSessionCheck();
		},
		logOut() {
			this.loggedIn = false;
			this.stopSessionCheck();
		},
	},
	getters: {
		isLoggedIn() {
			return this.loggedIn;
		}
	},
});
