<template>
	<StatusMessage :message=status.message :status=status.type @resetStatus="status.type = 'nope'"></StatusMessage>
	<div class="p-2 sm:p-7">
		<!-- Tab Navigation -->
		<div class="mb-6">
			<div class="border-b border-gray-200">
				<nav class="-mb-px flex space-x-8">
					<button
						@click="activeTab = 'organizations'"
						:class="[activeTab === 'organizations' ? 'border-ralpurple-500 text-ralpurple-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300', 'whitespace-nowrap pb-4 px-1 border-b-2 font-medium']">
						Organizations
					</button>
					<button
						@click="activeTab = 'demo-environments'"
						:class="[activeTab === 'demo-environments' ? 'border-ralpurple-500 text-ralpurple-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300', 'whitespace-nowrap pb-4 px-1 border-b-2 font-medium']">
						Demo Environments
					</button>
				</nav>
			</div>
		</div>

		<!-- Organizations Tab -->
		<div v-if="activeTab === 'organizations'">
			<div class="text-neutral-800 text-opacity-70 text-7xl font-normal font-['Open Sans']">Organizations</div>
			<input type="text" v-model="searchText" placeholder="Search organizations" class="mt-2 p-2 border rounded">
			<div class="mx-auto bg-white rounded-2xl border border-violet-300 bg-opacity-75 overflow-hidden shadow-md mb-2 px-6 py-6 mt-8">
				<div class="flex justify-between bg-gray-200 p-2 rounded-t-lg">
					<div class="w-1/6 text-left font-semibold">ID</div>
					<div class="w-2/6 text-left font-semibold">Name</div>
					<div class="w-2/6 text-left font-semibold">External Domain</div>
					<div class="w-1/6 text-left font-semibold">Actions</div>
				</div>
				<div v-for="org in paginatedOrganizations" :key="org.id" :class="{'bg-indigo-50': isLoggedIn(org.id)}" class="flex justify-between items-center p-2 border-t border-gray-300">
					<div class="w-1/6">{{ org.id }}</div>
					<div class="w-2/6">{{ org.name }}</div>
					<div class="w-2/6">{{ org.externalDomain || 'N/A' }}</div>
					<div class="w-1/6">
						<LightSecondaryButton cta="Log In" v-if="!isLoggedIn(org.id)" @click="logIn(org)"></LightSecondaryButton>
						<LightSecondaryButton cta="Log Out" v-if="isLoggedIn(org.id)" @click="logOut()"></LightSecondaryButton>
					</div>
				</div>
			</div>
			<div class="flex justify-between mt-4">
				<button @click="prevPage" class="bg-ralpurple-500 hover:bg-ralpurple-600 text-white font-bold py-2 px-4 rounded">
					Prev
				</button>
				<button @click="nextPage" class="bg-ralpurple-500 hover:bg-ralpurple-600 text-white font-bold py-2 px-4 rounded">
					Next
				</button>
			</div>
		</div>

		<!-- Demo Environments Tab -->
		<div v-if="activeTab === 'demo-environments'">
			<div class="text-neutral-800 text-opacity-70 text-7xl font-normal font-['Open Sans']">Demo Environments</div>

			<!-- Create Demo Environment Button -->
			<div class="mt-4">
				<button @click="showCreateDemoModal = true"
					:disabled="isCreatingDemo"
					class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
					<span v-if="isCreatingDemo" class="inline-flex items-center">
						<svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
							<circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
							<path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
						</svg>
						Creating Demo Environment...
					</span>
					<span v-else>Create Demo Environment</span>
				</button>
			</div>

			<!-- Demo Environments List -->
			<div class="mx-auto bg-white rounded-2xl border border-violet-300 bg-opacity-75 overflow-hidden shadow-md mb-2 px-6 py-6 mt-8">
				<div class="flex justify-between bg-gray-200 p-2 rounded-t-lg">
					<div class="w-1/6 text-left font-semibold">Store URL</div>
					<div class="w-1/6 text-left font-semibold">AOV</div>
					<div class="w-1/6 text-left font-semibold">LTV</div>
					<div class="w-1/6 text-left font-semibold">Default Email</div>
					<div class="w-1/6 text-left font-semibold">Default Password</div>
					<div class="w-1/6 text-left font-semibold">Actions</div>
				</div>
				<div v-for="demo in demoEnvironments" :key="demo.id" class="flex justify-between items-center p-2 border-t border-gray-300">
					<div class="w-1/6">{{ demo.storeUrl }}</div>
					<div class="w-1/6">${{ demo.aov }}</div>
					<div class="w-1/6">${{ demo.ltv }}</div>
					<div class="w-1/6">{{ demo.defaultEmail }}</div>
					<div class="w-1/6">{{ demo.defaultPassword ? '••••••••' : 'Not set' }}</div>
					<div class="w-1/6">
						<button @click="removeDemoEnvironment(demo.id)"
							:disabled="removingDemoId === demo.id"
							class="text-red-600 hover:text-red-800 disabled:opacity-50 disabled:cursor-not-allowed">
							<span v-if="removingDemoId === demo.id" class="inline-flex items-center">
								<svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-red-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
									<circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
									<path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
								</svg>
								Removing...
							</span>
							<span v-else>Remove</span>
						</button>
					</div>
				</div>
			</div>

			<!-- Create Demo Environment Modal -->
			<div v-if="showCreateDemoModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center">
				<div class="bg-white p-6 rounded-lg w-96">
					<h2 class="text-xl font-bold mb-4">Create Demo Environment</h2>
					<div class="space-y-4">
						<div>
							<label class="block text-sm font-medium text-gray-700">Store URL</label>
							<input v-model="newDemo.storeUrl" type="text" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-ralpurple-500 focus:ring-ralpurple-500">
						</div>
						<div>
							<label class="block text-sm font-medium text-gray-700">AOV</label>
							<input v-model="newDemo.aov" type="number" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-ralpurple-500 focus:ring-ralpurple-500">
						</div>
						<div>
							<label class="block text-sm font-medium text-gray-700">LTV</label>
							<input v-model="newDemo.ltv" type="number" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-ralpurple-500 focus:ring-ralpurple-500">
						</div>
						<div>
							<label class="block text-sm font-medium text-gray-700">Default Login Email</label>
							<input v-model="newDemo.defaultEmail" type="email" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-ralpurple-500 focus:ring-ralpurple-500">
						</div>
						<div>
							<label class="block text-sm font-medium text-gray-700">Default Password</label>
							<input v-model="newDemo.defaultPassword" type="password" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-ralpurple-500 focus:ring-ralpurple-500">
						</div>
						<div>
							<label class="block text-sm font-medium text-gray-700">Number of Users</label>
							<input v-model="newDemo.numberOfUsers" type="number" min="1" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-ralpurple-500 focus:ring-ralpurple-500">
						</div>
						<div class="flex justify-end space-x-3">
							<button @click="showCreateDemoModal = false" class="bg-gray-200 hover:bg-gray-300 px-4 py-2 rounded">
								Cancel
							</button>
							<button @click="createDemoEnvironment" class="bg-ralpurple-500 hover:bg-ralpurple-600 text-white px-4 py-2 rounded">
								Create
							</button>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script>

import * as Utils from '../../client-old/utils/Utils';
import CardContainer from '../components/CardContainer.ts.vue';
import LightSecondaryButton from '../components/LightSecondaryButton.ts.vue'
import ToggleItem from '../components/ToggleItem.ts.vue';
import StatusMessage from '../components/StatusMessage.ts.vue';
import { useRoute } from 'vue-router';
import * as userService from '../services/user.js'
import { getCurrentOrg } from '../services/organization.js'
import { Crisp } from 'crisp-sdk-web';

const URL_DOMAIN = Utils.URL_DOMAIN;

export default {
	components: {
		CardContainer,
		LightSecondaryButton,
		StatusMessage,
		ToggleItem
	},
        async mounted() {

                const route = useRoute();
                const currentOrg = await getCurrentOrg();
                this.isDemoOrg = currentOrg?.isDemoOrg;

                await this.getAvailableOrgs();

                if (route.query.code && route.query.name) {
                        this.exchangeCode(route.query.code, route.query.name);
                }
                await this.fetchDemoEnvironments();
                window.addEventListener('beforeunload', this.handleBeforeUnload);
        },
	beforeDestroy() {
		window.removeEventListener('beforeunload', this.handleBeforeUnload);
	},
	data() {
		return {
			status: {},
			isConnected: false,
			isActive: true,
			activeState: true,
			availableOrgs: [],
			exchangingCode: false,
			currentPage: 1,
			pageSize: 10,
			searchText: '',
			filteredOrgs: [],
			activeTab: 'organizations',
			demoEnvironments: [], // Will be populated by API call
			showCreateDemoModal: false,
			isCreatingDemo: false,
			removingDemoId: null,
                        newDemo: {
                                storeUrl: '',
                                aov: 0,
                                ltv: 0,
                                defaultEmail: '',
                                defaultPassword: '',
                                numberOfUsers: 1
                        },
                        isDemoOrg: false
                };
        },
	computed: {
		filteredOrganizations() {
			const userOrgId = localStorage.getItem('userOrgId');
			let orgs = this.availableOrgs.filter(org =>
				org.id.toString().includes(this.searchText) ||
				org.name.toLowerCase().includes(this.searchText.toLowerCase()) ||
				(org.externalDomain && org.externalDomain.toLowerCase().includes(this.searchText.toLowerCase()))
			);

			const loggedInOrgIndex = orgs.findIndex(org => org.id.toString() === userOrgId);
			if (loggedInOrgIndex > -1) {
				const [loggedInOrg] = orgs.splice(loggedInOrgIndex, 1);
				orgs.unshift(loggedInOrg);
			}

			return orgs;
		},
		paginatedOrganizations() {
			const start = (this.currentPage - 1) * this.pageSize;
			return this.filteredOrganizations.slice(start, start + this.pageSize);
		}
	},
	methods: {
		handleBeforeUnload(event) {
			if (this.isCreatingDemo || this.removingDemoId) {
				event.preventDefault();
				event.returnValue = '';
			}
		},
		openDocs(integration) {
			window.open(integration.docURL, '_blank');
		},
		openChat() {
			Crisp.chat.open();
		},
		setStatus(data) {
			this.status.type = data.type;
			this.status.message = data.message;
		},
                async getAvailableOrgs() {
                        const response = await fetch(`${URL_DOMAIN}/admin/organizations`, {
                                method: 'GET',
                                headers: {
                                        'Content-Type': 'application/json',
                                        'Authorization': `Bearer ${localStorage.getItem('token')}`,
                                }
                        });
                        const jsonresponse = await response.json();
                        console.log("Response from plans list:", jsonresponse);
                        if (this.isDemoOrg) {
                                const currentId = parseInt(localStorage.getItem('userOrgId'));
                                this.availableOrgs = jsonresponse.filter(o => o.id === currentId);
                        } else {
                                this.availableOrgs = jsonresponse;
                        }
                },
                async logIn(selectedOrg) {
                        if (this.isDemoOrg && selectedOrg.id !== parseInt(localStorage.getItem('userOrgId'))) {
                                this.setStatus({
                                        message: 'Demo users cannot switch organizations',
                                        type: 'error'
                                });
                                return;
                        }

                        const loginRequest = await fetch(`${URL_DOMAIN}/admin/login/${selectedOrg.id}`, {
                                method: 'POST',
                                credentials: 'omit', // include, *same-origin, omit
                                headers: {
                                        Authorization: `Bearer ${localStorage.getItem('token')}`,
                                        'Content-Type': 'application/json',
                                }
                        });
			const loginResult = await loginRequest.json();
			console.log(loginResult);
			if (loginResult.token) {
				localStorage.setItem('token', loginResult.token);
				let userInfo = await userService.getUserInfo();
				localStorage.removeItem('metricsCache');
				await userService.setUserInfoSignin(userInfo);
				if (this.redirect == 'docs') {
					this.docLogin();
				}
				else {
					// Refresh the Support page instead of navigating away
					setTimeout(() => {
						this.$router.go(0); // Refresh the current route
					}, 100);
					return;
				}
			} else {
				this.loginError = true;
			}
		},
                async logOut() {
                        if (this.isDemoOrg) {
                                this.setStatus({
                                        message: 'Demo users cannot switch organizations',
                                        type: 'error'
                                });
                                return;
                        }

                        const loginRequest = await fetch(`${URL_DOMAIN}/admin/login/1`, {
                                method: 'POST',
                                credentials: 'omit', // include, *same-origin, omit
                                headers: {
                                        Authorization: `Bearer ${localStorage.getItem('token')}`,
                                        'Content-Type': 'application/json',
                                }
                        });
			const loginResult = await loginRequest.json();
			console.log(loginResult);
			if (loginResult.token) {
				localStorage.setItem('token', loginResult.token);
				let userInfo = await userService.getUserInfo();
				await userService.setUserInfoSignin(userInfo);
				if (this.redirect == 'docs') {
					this.docLogin();
				}
				else {
					// Refresh the Support page instead of navigating away
					setTimeout(() => {
						this.$router.go(0); // Refresh the current route
					}, 100);
					return;
				}
			} else {
				this.loginError = true;
			}
		},
		isLoggedIn(orgId) {
			return localStorage.getItem('userOrgId') == orgId;
		},
		nextPage() {
			if ((this.currentPage * this.pageSize) < this.filteredOrganizations.length) this.currentPage++;
		},
		prevPage() {
			if (this.currentPage > 1) this.currentPage--;
		},

		// Demo Environment Methods
		async fetchDemoEnvironments() {
			try {
				const response = await fetch(`${URL_DOMAIN}/demo-environments`, {
					method: 'GET',
					headers: {
						'Content-Type': 'application/json',
						'Authorization': `Bearer ${localStorage.getItem('token')}`,
					}
				});
				const data = await response.json();
				this.demoEnvironments = data || [];
			} catch (error) {
				console.error('Error fetching demo environments:', error);
				this.setStatus({
					message: 'Failed to fetch demo environments',
					type: 'error'
				});
			}
		},

		async createDemoEnvironment() {
			try {
				this.isCreatingDemo = true;
				this.showCreateDemoModal = false; // Close modal immediately when starting
				const response = await fetch(`${URL_DOMAIN}/demo-environments`, {
					method: 'POST',
					headers: {
						'Content-Type': 'application/json',
						'Authorization': `Bearer ${localStorage.getItem('token')}`,
					},
					body: JSON.stringify({
						storeUrl: this.newDemo.storeUrl,
						aov: this.newDemo.aov,
						ltv: this.newDemo.ltv,
						defaultEmail: this.newDemo.defaultEmail,
						defaultPassword: this.newDemo.defaultPassword,
						numberOfUsers: this.newDemo.numberOfUsers
					})
				});

				const data = await response.json();
				this.demoEnvironments.push(data);
				await this.fetchDemoEnvironments();
				this.newDemo = { storeUrl: '', aov: 0, ltv: 0, defaultEmail: '', defaultPassword: '', numberOfUsers: 1 };

				this.setStatus({
					message: 'Demo environment created successfully',
					type: 'success'
				});
			} catch (error) {
				console.error('Error creating demo environment:', error);
				this.setStatus({
					message: 'Failed to create demo environment',
					type: 'error'
				});
			} finally {
				this.isCreatingDemo = false;
			}
		},

		async removeDemoEnvironment(id) {
			this.removingDemoId = id;
			try {
				const response = await fetch(`${URL_DOMAIN}/demo-environments/${id}`, {
					method: 'DELETE',
					headers: {
						'Authorization': `Bearer ${localStorage.getItem('token')}`,
					}
				});

				if (response.ok) {
					this.demoEnvironments = this.demoEnvironments.filter(demo => demo.id !== id);
					this.setStatus({
						message: 'Demo environment removed successfully',
						type: 'success'
					});
				} else {
					throw new Error('Failed to delete demo environment');
				}
			} catch (error) {
				console.error('Error removing demo environment:', error);
				this.setStatus({
					message: 'Failed to remove demo environment',
					type: 'error'
				});
			} finally {
				this.removingDemoId = null;
			}
		}
	},
}
</script>
