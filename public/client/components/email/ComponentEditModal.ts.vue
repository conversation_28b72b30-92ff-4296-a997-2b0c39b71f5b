<template>
  <div v-if="show" class="fixed inset-0 overflow-y-auto z-50 transition-all">
    <div class="flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0">
      <div class="fixed inset-0 transition-opacity" aria-hidden="true">
        <div class="absolute inset-0 bg-gray-800 opacity-75"></div>
      </div>
      <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
      <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-4xl sm:w-full">
        <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
          <div class="sm:flex sm:items-start">
            <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
              <h3 class="text-lg leading-6 font-medium text-gray-900">
                {{ componentMode === 'create' ? 'Create Custom Component' : 'Override Component' }}
              </h3>
              <div class="mt-4 mb-2">
                <div class="flex justify-between items-center mb-2">
                  <label class="block text-sm font-medium text-gray-700">Component Name</label>
                  <div class="flex items-center" v-if="componentMode === 'override' || (componentMode === 'create' && component.orgId === orgId)">
                    <span class="text-sm text-gray-700 mr-2">Active:</span>
                    <ToggleItem
                      :state="component.active !== false"
                      @toggleChange="component.active = $event"
                      :showLabel="false"
                    />
                  </div>
                </div>
                <input type="text" v-model="component.name" class="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md">
              </div>

              <!-- Component Type Selection (for overriding only) -->
              <div v-if="componentMode === 'override' && !editingComponent" class="mt-4 mb-4">
                <label class="block text-sm font-medium text-gray-700 mb-1">Component to Override</label>
                <select v-model="component.id" @change="onComponentSelect" class="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md">
                  <option value="">Select a component</option>
                  <option v-for="comp in globalComponents" :key="comp.id" :value="comp.id">
                    {{ comp.name }}
                  </option>
                </select>
              </div>

              <!-- AI Instructions Section -->
              <div class="mt-4">
                <label class="block text-sm font-medium text-gray-700 mb-1">AI Instructions</label>
                <textarea v-model="component.description" rows="3" class="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md" placeholder="Enter instructions for AI to use when editing this component"></textarea>
                <p class="text-xs text-gray-500 mt-1">These instructions will be used by AI to understand how to edit this component.</p>
              </div>

              <!-- JSON Section -->
              <div class="mt-4">
                <label class="block text-sm font-medium text-gray-700 mb-1">Component JSON</label>
                <div class="relative">
                  <textarea v-model="component.json" @input="parseComponentJson" rows="10" class="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md font-mono"></textarea>
                </div>
                <div v-if="jsonError" class="mt-1 text-red-500 text-sm">{{ jsonError }}</div>
                <div v-if="detectedComponentTypes && detectedComponentTypes.length > 0" class="mt-1 text-sm text-green-600">
                  Detected component types: {{ detectedComponentTypes.join(', ') }}
                </div>
              </div>

              <!-- Editable AI Fields Section -->
              <EditableFieldsSection
                v-if="parsedComponentJson"
                :editable-fields="editableFields"
                :grouped-field-paths="groupedFieldPaths"
                :component-types-map="componentTypesMap"
                :component-names-map="componentNamesMap"
              />
            </div>
          </div>
        </div>

        <!-- Modal Footer -->
        <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
          <button @click="save" type="button" class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:ml-3 sm:w-auto sm:text-sm">
            Save
          </button>
          <button @click="close" type="button" class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm">
            Cancel
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import ToggleItem from '../ToggleItem.ts.vue';
import EditableFieldsSection from './EditableFieldsSection.ts.vue';
import { reactive } from 'vue';
import ComponentJsonParser from './ComponentJsonParser';

export default {
  name: 'ComponentEditModal',
  components: {
    ToggleItem,
    EditableFieldsSection
  },
  props: {
    show: {
      type: Boolean,
      default: false
    },
    componentMode: {
      type: String,
      default: 'override'
    },
    initialComponent: {
      type: Object,
      default: () => ({
        id: null,
        name: '',
        description: '',
        json: '',
        type: 'unlayer',
        editableFields: {},
        active: true
      })
    },
    editingComponent: {
      type: Boolean,
      default: false
    },
    globalComponents: {
      type: Array,
      default: () => []
    },
    orgId: {
      type: Number,
      required: true
    }
  },
  emits: ['close', 'save', 'component-select'],
  data() {
    return {
      component: { ...this.initialComponent },
      jsonError: '',
      parsedComponentJson: null,
      editableFields: reactive({}),
      detectedComponentTypes: [],
      componentTypesMap: {},
      componentNamesMap: {},
    };
  },
  watch: {
    initialComponent: {
      handler(newVal) {
        this.component = { ...newVal };
        this.parseComponentJson();
      },
      deep: true
    },
    show(newVal) {
      if (newVal) {
        this.component = { ...this.initialComponent };
        this.jsonError = '';
        this.editableFields = reactive({});
        this.parseComponentJson();
      }
    }
  },
  computed: {
    groupedFieldPaths() {
      if (!this.parsedComponentJson) return {};

      const result = {};
      const allPaths = ComponentJsonParser.extractPaths(this.parsedComponentJson);

      allPaths.forEach(path => {
        const componentId = this.getComponentIdFromPath(path);
        if (componentId) {
          if (!result[componentId]) {
            result[componentId] = [];
          }
          result[componentId].push(path);
        }
      });

      return result;
    }
  },
  methods: {
    close() {
      this.$emit('close');
    },
    save() {
      // Validate JSON
      try {
        JSON.parse(this.component.json);
      } catch (e) {
        this.jsonError = 'Invalid JSON structure';
        return;
      }

      // For both modes, component name is required
      if (!this.component.name || this.component.name.trim() === '') {
        this.jsonError = 'Component name is required';
        return;
      }

      // Create a simple object mapping paths to descriptions
      const editableFieldsToSave = {};

      // Loop through all editable fields and extract those that are marked as editable
      Object.entries(this.editableFields).forEach(([path, data]) => {
        if (data && data.editable === true) {
          // Make sure we're not saving indices directly
          if (!path.match(/^\d+$/)) {
            // Save with the description as a string value
            editableFieldsToSave[path] = data.description || '';
          }
        }
      });

      // Prepare the component data for saving
      const componentData = {
        ...this.component,
        editableFields: editableFieldsToSave
      };

      this.$emit('save', componentData);
    },
    onComponentSelect() {
      this.$emit('component-select', this.component.id);
    },
    parseComponentJson() {
      try {
        const json = this.component.json;
        if (!json) {
          this.parsedComponentJson = null;
          this.detectedComponentTypes = [];
          return;
        }

        this.parsedComponentJson = JSON.parse(json);
        this.detectComponentTypes();

        // Only initialize editableFields structure if it's empty
        // This prevents wiping out fields during editing
        const editableFieldsEmpty = Object.keys(this.editableFields).length === 0;

        if (editableFieldsEmpty) {
          // Initialize the editable fields structure
          const allPaths = ComponentJsonParser.extractPaths(this.parsedComponentJson);
          allPaths.forEach(path => {
            this.editableFields[path] = { editable: false, description: '' };
          });

          // If we're editing a component with existing editable fields, initialize them
          if (this.editingComponent && this.component.editableFields) {
            const fields = typeof this.component.editableFields === 'string'
              ? JSON.parse(this.component.editableFields)
              : this.component.editableFields;

            Object.keys(fields).forEach(key => {
              if (this.editableFields[key]) {
                this.editableFields[key].editable = true;

                // Set description if it exists
                if (typeof fields[key] === 'string') {
                  this.editableFields[key].description = fields[key];
                }
              }
            });
          }
        }
      } catch (e) {
        console.error('Error parsing component JSON:', e);
        this.parsedComponentJson = null;
        this.detectedComponentTypes = [];
        this.jsonError = 'Invalid JSON structure';
      }
    },
    detectComponentTypes() {
      this.detectedComponentTypes = [];
      if (!this.parsedComponentJson || !this.parsedComponentJson.body || !this.parsedComponentJson.body.rows) {
        return;
      }

      try {
        // Extract component types
        this.parsedComponentJson.body.rows.forEach(row => {
          if (row.columns) {
            row.columns.forEach(column => {
              if (column.contents) {
                column.contents.forEach(content => {
                  if (content.type && !this.detectedComponentTypes.includes(content.type)) {
                    this.detectedComponentTypes.push(content.type);
                  }

                  // Store the component type for this ID
                  if (content.type && content.id) {
                    this.componentTypesMap[content.id] = content.type;

                    // Try to extract a friendly name for the component
                    if (content.values) {
                      if (content.values.text) {
                        // Extract text without HTML
                        const textWithoutHtml = content.values.text.replace(/<[^>]*>/g, '');
                        this.componentNamesMap[content.id] = textWithoutHtml.substring(0, 20) +
                          (textWithoutHtml.length > 20 ? '...' : '');
                      } else if (content.values._meta && content.values._meta.htmlID) {
                        this.componentNamesMap[content.id] = content.values._meta.htmlID;
                      }
                    }

                    if (!this.componentNamesMap[content.id]) {
                      this.componentNamesMap[content.id] = `Component ${content.id.substring(0, 8)}`;
                    }
                  }
                });
              }
            });
          }
        });
      } catch (e) {
        console.error('Error detecting component types:', e);
      }
    },
    getComponentIdFromPath(path) {
      const match = path.match(/body\.rows\[(\d+)\]\.columns\[(\d+)\]\.contents\[(\d+)\]/);
      if (match && this.parsedComponentJson) {
        const rowIndex = parseInt(match[1]);
        const colIndex = parseInt(match[2]);
        const contentIndex = parseInt(match[3]);
        try {
          return this.parsedComponentJson.body.rows[rowIndex].columns[colIndex].contents[contentIndex].id || null;
        } catch (e) {
          console.error('Error extracting component ID from path:', e);
          return null;
        }
      }
      return null;
    }
  }
};
</script>
