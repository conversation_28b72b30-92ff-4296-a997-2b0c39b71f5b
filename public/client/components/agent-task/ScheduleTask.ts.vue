<template>
  <div class="h-full">
    <div class="h-full">
      <div class="px-6 py-3 border-b bg-white">
        <h2 class="text-lg font-medium">Suggested Send Date</h2>
      </div>

      <div class="p-6 space-y-6 max-w-3xl">
        <!-- Date Suggestions -->
        <div class="space-y-4">
          <div class="w-full bg-white text-left p-4 border rounded-lg hover:border-purple-300 transition-colors">
            <div class="flex items-start justify-between">
              <div class="space-y-2">
                <div class="flex items-center gap-2">
                  <svg class="w-4 h-4 text-gray-500" viewBox="0 0 24 24">
                    <path fill="currentColor"
                      d="M19 4h-1V2h-2v2H8V2H6v2H5c-1.11 0-1.99.9-1.99 2L3 20c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 16H5V9h14v11z" />
                  </svg>
                  <!-- Display mode -->
                  <div v-if="!isEditingDate" class="flex items-center">
                    <span class="font-medium">{{ formatScheduledTime(currentScheduledDate) }}</span>
                    <button
                      @click="toggleDateEdit"
                      class="ml-2 text-gray-400 hover:text-blue-500 focus:outline-none"
                      title="Edit send date"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                        <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z" />
                      </svg>
                    </button>
                  </div>

                  <!-- Edit mode -->
                  <div v-else class="flex items-center">
                    <div class="relative">
                      <flat-pickr
                        v-model="scheduledDate"
                        :config="dateConfig"
                        class="form-input pl-3 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                        style="min-width: 250px;"
                      ></flat-pickr>
                    </div>
                    <button
                      @click="saveDate"
                      :disabled="isSaving"
                      class="ml-2 text-gray-400 hover:text-green-500 focus:outline-none"
                      title="Save date"
                    >
                      <svg v-if="!isSaving" xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                      </svg>
                      <svg v-else class="animate-spin h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                    </button>
                    <button
                      @click="cancelEdit"
                      class="ml-2 text-gray-400 hover:text-red-500 focus:outline-none"
                      title="Cancel editing"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
                      </svg>
                    </button>
                  </div>
                </div>
                <p class="text-sm text-gray-600">{{ task?.campaign?.whyText }}</p>
                <div v-if="errorMessage" class="text-sm text-red-500 mt-1">{{ errorMessage }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import flatPickr from 'vue-flatpickr-component';
import 'flatpickr/dist/flatpickr.css';

// Import URL_DOMAIN for API calls
import * as Utils from '../../../client-old/utils/Utils';
const URL_DOMAIN = Utils.URL_DOMAIN;

export default {
  name: 'ScheduleTask',
  components: {
    flatPickr
  },
  props: {
    task: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      isEditingDate: false,
      scheduledDate: null,
      currentScheduledDate: null,
      isSaving: false,
      errorMessage: '',
      dateConfig: {
        enableTime: false,
        dateFormat: "Y-m-d",
        altInput: true,
        altFormat: "F j, Y",
        static: true,
        monthSelectorType: 'static',
        utc: true,
        allowInput: true,
        // Force flatpickr to use UTC for all operations and prevent timezone adjustment
        formatDate: (date, format) => {
          // Custom formatter ensures consistent date output regardless of timezone
          if (format === "Y-m-d") {
            return `${date.getUTCFullYear()}-${String(date.getUTCMonth() + 1).padStart(2, '0')}-${String(date.getUTCDate()).padStart(2, '0')}`;
          }
          // For alt format (display format)
          const months = ["January", "February", "March", "April", "May", "June", "July", "August", "September", "October", "November", "December"];
          return `${months[date.getUTCMonth()]} ${date.getUTCDate()}, ${date.getUTCFullYear()}`;
        },
        // Custom parser to ensure dates are parsed in UTC mode
        parseDate: (dateStr, format) => {
          if (dateStr.match(/^\d{4}-\d{2}-\d{2}$/)) {
            const [year, month, day] = dateStr.split('-').map(num => parseInt(num, 10));
            return new Date(Date.UTC(year, month - 1, day));
          }
          return new Date(dateStr);
        },
        prevArrow: '<svg class="fill-current" width="7" height="11" viewBox="0 0 7 11"><path d="M5.4 10.8l1.4-1.4-4-4 4-4L5.4 0 0 5.4z" /></svg>',
        nextArrow: '<svg class="fill-current" width="7" height="11" viewBox="0 0 7 11"><path d="M1.4 10.8L0 9.4l4-4-4-4L1.4 0l5.4 5.4z" /></svg>',
      }
    };
  },
  watch: {
    'task.campaign.scheduledDate': {
      immediate: true,
      handler(newDate, oldDate) {
        console.log('Task scheduledDate changed in ScheduleTask:', {
          oldDate: oldDate,
          newDate: newDate,
          formatted: newDate ? this.formatScheduledTime(newDate) : null
        });
        if (newDate) {
          this.scheduledDate = newDate;
          this.currentScheduledDate = newDate;
        }
      }
    },
    task: {
      immediate: true,
      deep: true,
      handler(newTask) {
        console.log('Task object changed in ScheduleTask:', newTask);
      }
    }
  },
  methods: {
    formatScheduledTime(date) {
      if (!date) return '';

      console.log('ScheduleTask formatScheduledTime input:', date);

      try {
        // Make sure we have a valid date object
        // Handle YYYY-MM-DD string format (from API)
        if (typeof date === 'string' && date.match(/^\d{4}-\d{2}-\d{2}$/)) {
          // Extract year, month, day directly from string
          const [year, month, day] = date.split('-').map(num => parseInt(num, 10));

          // Use UTC date creation to avoid any timezone offset issues
          const utcDate = new Date(Date.UTC(year, month - 1, day, 12, 0, 0));
          console.log('ScheduleTask parsed date (UTC):', utcDate.toISOString());

          // Format the date in a timezone-independent way using UTC
          return utcDate.toLocaleDateString('en-US', {
            weekday: 'long',
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            timeZone: 'UTC' // Force UTC timezone for consistent display
          });
        }
        // Handle Date object (from date picker)
        else if (date instanceof Date) {
          console.log('ScheduleTask formatting Date object:', date.toISOString());
          // Convert to UTC to avoid timezone issues
          return date.toLocaleDateString('en-US', {
            weekday: 'long',
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            timeZone: 'UTC' // Force UTC timezone for consistent display
          });
        }

        // Fallback for other string formats
        console.warn('Non-standard date format in ScheduleTask:', date);
        const parsedDate = new Date(date);
        if (!isNaN(parsedDate.getTime())) {
          return parsedDate.toLocaleDateString('en-US', {
            weekday: 'long',
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            timeZone: 'UTC'
          });
        }

        return 'Invalid date';
      } catch (error) {
        console.error('Error formatting date in ScheduleTask:', error);
        return 'Date Error';
      }
    },
    toggleDateEdit() {
      this.isEditingDate = true;

      // We need to ensure the date picker gets the correct date without timezone shifts
      if (this.task?.campaign?.scheduledDate) {
        const dateString = this.task.campaign.scheduledDate;
        console.log('Toggle edit - original date string:', dateString);

        // If it's a YYYY-MM-DD format (most common case)
        if (typeof dateString === 'string' && dateString.match(/^\d{4}-\d{2}-\d{2}$/)) {
          // Parse the date components directly
          const [year, month, day] = dateString.split('-').map(num => parseInt(num, 10));

          // Create a UTC-based date object (which flatpickr will handle correctly with utc: true)
          // The +1 day adjustment compensates for timezone shifts in the date picker
          const utcDate = new Date(Date.UTC(year, month - 1, day));
          console.log('Toggle edit - UTC date created:', utcDate.toISOString());

          // Set directly to ensure no timezone conversions happen
          this.scheduledDate = dateString;
        } else {
          // For other formats, just use the string directly
          this.scheduledDate = dateString;
        }
      }
    },
    cancelEdit() {
      this.scheduledDate = this.task?.campaign?.scheduledDate;
      this.isEditingDate = false;
      this.errorMessage = '';
    },
    async saveDate() {
      try {
        if (!this.scheduledDate) {
          console.error('No date selected');
          return;
        }

        this.isSaving = true;
        this.errorMessage = '';

        // Get the campaign ID from the task
        const campaignId = this.task?.campaign?.id;
        if (!campaignId) {
          throw new Error('Campaign ID not found');
        }

        // Ensure date is properly formatted as YYYY-MM-DD
        let formattedDate = '';

        if (this.scheduledDate instanceof Date) {
          // Format as YYYY-MM-DD for API requirement using UTC to avoid timezone shifts
          // This ensures consistent date handling regardless of the user's timezone
          const year = this.scheduledDate.getUTCFullYear();
          const month = String(this.scheduledDate.getUTCMonth() + 1).padStart(2, '0');
          const day = String(this.scheduledDate.getUTCDate()).padStart(2, '0');
          formattedDate = `${year}-${month}-${day}`;
          console.log('Formatted from Date object (UTC):', formattedDate);
        } else if (typeof this.scheduledDate === 'string') {
          // Handle string date formats
          if (this.scheduledDate.includes('T')) {
            // ISO string format, extract date part
            formattedDate = this.scheduledDate.split('T')[0];
            console.log('Formatted from ISO string:', formattedDate);
          } else if (this.scheduledDate.match(/^\d{4}-\d{2}-\d{2}$/)) {
            // Already in YYYY-MM-DD format
            formattedDate = this.scheduledDate;
            console.log('Already in YYYY-MM-DD format:', formattedDate);
          } else {
            // Try to parse and format the date - use our timezone-safe approach
            try {
              const [year, month, day] = this.scheduledDate.split(/[-\/]/).map(num => parseInt(num, 10));
              if (!isNaN(year) && !isNaN(month) && !isNaN(day)) {
                // Create date using local components to avoid timezone issues
                const parsedDate = new Date(year, month - 1, day, 12, 0, 0); // noon local time
                if (!isNaN(parsedDate.getTime())) {
                  formattedDate = `${year}-${String(month).padStart(2, '0')}-${String(day).padStart(2, '0')}`;
                  console.log('Parsed and formatted custom date:', formattedDate);
                } else {
                  throw new Error('Invalid date components');
                }
              } else {
                throw new Error('Could not parse date components');
              }
            } catch (parseError) {
              console.error('Parse error:', parseError);
              // Fallback to standard date parsing as a last resort
              const parsedDate = new Date(this.scheduledDate);
              if (!isNaN(parsedDate.getTime())) {
                const year = parsedDate.getFullYear();
                const month = String(parsedDate.getMonth() + 1).padStart(2, '0');
                const day = String(parsedDate.getDate()).padStart(2, '0');
                formattedDate = `${year}-${month}-${day}`;
                console.log('Fallback date parsing:', formattedDate);
              } else {
                throw new Error('Invalid date format');
              }
            }
          }
        } else {
          throw new Error('Unrecognized date format');
        }

        console.log('Sending date to API:', formattedDate);

        // Validate the date format before sending
        if (!formattedDate.match(/^\d{4}-\d{2}-\d{2}$/)) {
          throw new Error('Date format invalid. Expected YYYY-MM-DD.');
        }

        // Make the API call to update the campaign
        console.log('Making API call to update campaign date:', {
          url: `${URL_DOMAIN}/planner/campaign/${campaignId}`,
          data: { scheduledDate: formattedDate }
        });

        const response = await fetch(`${URL_DOMAIN}/planner/campaign/${campaignId}`, {
          method: 'PATCH',
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            scheduledDate: formattedDate
          }),
        });

        console.log('API Response status:', response.status);
        const result = await response.json();
        console.log('API Response data:', result);

        if (result.status === 200) {
          // The API might be adjusting the date due to timezone issues
          // Let's check if the returned date is what we expect
          let dateToUse = formattedDate;

          if (result.data && result.data.scheduledDate) {
            console.log('API returned scheduledDate:', result.data.scheduledDate);

            // Check if the API-returned date is different from what we sent
            if (result.data.scheduledDate !== formattedDate) {
              console.warn('API returned different date than we sent:', {
                sent: formattedDate,
                received: result.data.scheduledDate
              });

              // We need to ensure we use the actual date string we sent
              // This helps prevent any timezone shifts in the display
              dateToUse = formattedDate;
            }
          }

          // Update local state for immediate UI feedback with our original formatted date
          this.currentScheduledDate = dateToUse;
          console.log('Updated currentScheduledDate:', this.currentScheduledDate);

          // Create a deep copy of the task to avoid mutation issues
          const updatedTask = JSON.parse(JSON.stringify(this.task));

          // Update the task's scheduled date with our controlled date string
          if (updatedTask.campaign) {
            updatedTask.campaign.scheduledDate = dateToUse;
            console.log('Updated task campaign scheduledDate:', updatedTask.campaign.scheduledDate);
          }

          // Also update our local scheduledDate to match
          this.scheduledDate = dateToUse;

          // Emit task-updated event to parent component - this is the ONLY event we need
          console.log('Emitting task-updated event');
          this.$emit('task-updated', updatedTask);

          // Switch back to display mode
          this.isEditingDate = false;
        } else {
          throw new Error(result.message || 'Failed to update campaign date');
        }
      } catch (error) {
        console.error('Error saving date:', error);
        this.errorMessage = error.message || 'Failed to save date. Please try again.';
        // Provide feedback to user
        this.$emit('error', this.errorMessage);
      } finally {
        this.isSaving = false;
      }
    }
  }
}
</script>
