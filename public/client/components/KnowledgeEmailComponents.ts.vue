<template>
  <div class="email-components-container p-6 space-y-6">
    <div class="bg-white rounded-lg border shadow-sm">
      <div class="p-6">
        <h2 class="text-lg font-semibold">Custom Email Components</h2>
        <p class="text-sm text-gray-500">Customize your email components for this brand only. These will override the global components.</p>

        <div class="mt-4 flex justify-between items-center">
          <div>
            <div class="flex items-center space-x-4">
              <label class="text-sm font-medium flex items-center">
                <input
                  type="checkbox"
                  :checked="hasCustomComponents"
                  @change="$emit('update:hasCustomComponents', $event.target.checked)"
                  class="mr-2 h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded"
                />
                Enable Custom Email Components
              </label>
            </div>
            <p class="text-xs text-gray-500 mt-1">
              When enabled, you can customize email components for your specific brand needs
            </p>
          </div>
        </div>
      </div>

      <div v-if="hasCustomComponents" class="p-6 pt-0 space-y-4">
        <!-- Loading indicator -->
        <div v-if="isLoadingComponents" class="flex justify-center py-8">
          <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600"></div>
        </div>

        <!-- Component list -->
        <ComponentList
          :components="allComponents"
          :loading="loading"
          :show-inactive="showInactive"
          :org-id="orgId"
          @update:show-inactive="showInactive = $event"
          @open-modal="openComponentModal"
          @toggle-active="toggleComponentActive"
          @delete-component="deleteComponent"
          @debug-component="debugComponent"
        />
      </div>
    </div>
  </div>

  <!-- Component Edit Modal -->
  <ComponentEditModal
    :show="showComponentModal"
    :component-mode="componentMode"
    :initial-component="currentComponent"
    :editing-component="editingComponent"
    :global-components="globalEmailComponents"
    :org-id="orgId"
    @close="closeComponentModal"
    @save="saveComponentOverride"
    @component-select="onComponentSelect"
  />
</template>

<script>
import * as OrgServices from '../services/organization';
import * as Utils from '../../client-old/utils/Utils';
import { reactive } from 'vue';
import ToggleItem from './ToggleItem.ts.vue';
import ComponentList from './email/ComponentList.ts.vue';
import ComponentEditModal from './email/ComponentEditModal.ts.vue';
import ComponentJsonParser from './email/ComponentJsonParser';

const URL_DOMAIN = Utils.URL_DOMAIN;

export default {
  name: 'KnowledgeEmailComponents',
  components: {
    ToggleItem,
    ComponentList,
    ComponentEditModal
  },
  props: {
    hasCustomComponents: {
      type: Boolean,
      required: true
    },
    orgId: {
      type: Number,
      required: true
    },
    overrideId: {
      type: Number,
      required: true
    }
  },
  emits: ['update:hasCustomComponents', 'status-update'],
  data() {
    return {
      // Component state
      loading: false,
      isLoadingComponents: false,
      globalEmailComponents: [],
      expandedComponents: {},
      showInactive: false, // Whether to show inactive components

      // Modal state
      showComponentModal: false,
      currentComponent: {
        id: null,
        name: '',
        description: '',
        json: '',
        type: 'unlayer',
        editableFields: {},
        active: true
      },
      jsonError: '',
      editingComponent: false,
      componentMode: 'override',
    };
  },
  created() {
    // Load components (includes both global and overridden components)
    this.loadUnlayerComponents();
  },
  methods: {
    loadEmailComponents() {
      this.isLoadingComponents = true;
      try {
        fetch(`${URL_DOMAIN}/unlayer-components`, {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`,
          },
        })
        .then(response => response.json())
        .then(data => {
          this.globalEmailComponents = data;

          // Initialize expansion state for each component
          this.globalEmailComponents.forEach(component => {
            if (component.id) {
              this.expandedComponents[component.id] = false;
            }
          });
          this.isLoadingComponents = false;
        });
      } catch (error) {
        console.error('Failed to load email components:', error);
        this.$emit('status-update', 'error', 'Failed to load email components');
        this.isLoadingComponents = false;
      }
    },
    isComponentOverridden(componentId) {
      // Check if this component is overridden by a component with overrideId matching this componentId
      return this.hasCustomComponents &&
        this.globalEmailComponents.some(comp =>
          comp.overrideId === componentId &&
          comp.orgId === this.orgId
        );
    },
    getComponentJson(componentId) {
      // Find the component in the global components list
      const component = this.globalEmailComponents.find(c => c.id === componentId);

      // If component exists, return its JSON, otherwise return empty object
      return component ? component.json : '{}';
    },
    toggleComponentExpand(componentId) {
      this.expandedComponents[componentId] = !this.expandedComponents[componentId];
    },
    toggleComponentActive(component, isActive) {
      // For global components, create an override with active=false
      if (component.orgId === null || component.orgId === undefined) {
        if (!isActive) {
          // Create an override of the global component with active=false
          this.createDeactivatedCopy(component);
        }
        return;
      }

      // For organization-specific components, update the active state
      // Update the component's active state
      const componentData = {
        active: isActive
      };

      // Save the updated component to the server
      fetch(`${URL_DOMAIN}/unlayer-components/${component.id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify(componentData)
      })
      .then(response => {
        if (!response.ok) {
          throw new Error(`HTTP error ${response.status}`);
        }

        // Try to parse the response, but don't fail if it's not valid JSON
        return response.json().catch(err => {
          console.warn('Could not parse JSON response, but proceeding anyway:', err);
          return { success: true };
        });
      })
      .then(result => {
        // Update the component in the local list
        component.active = isActive;

        // Show success message
        this.$emit('status-update', 'success', `Component ${isActive ? 'activated' : 'deactivated'} successfully.`);
      })
      .catch(error => {
        console.error('Error updating component active state:', error);
        this.$emit('status-update', 'error', 'Failed to update component: ' + (error.message || 'Unknown error'));
      });
    },
    toggleComponentOverride(component) {
      if (this.isComponentOverridden(component.id)) {
        // Find the overridden component (the one with overrideId matching this component's id)
        const overriddenComponent = this.getOverrideComponent(component.id);

        if (overriddenComponent) {
          // Delete the override via API
          const url = new URL(`${URL_DOMAIN}/unlayer-components/${overriddenComponent.id}`);
          url.searchParams.append('orgId', this.orgId.toString());

          fetch(url.toString(), {
            method: 'DELETE',
            headers: {
              'Authorization': `Bearer ${localStorage.getItem('token')}`
            }
          })
          .then(response => {
            if (!response.ok) {
              throw new Error(`HTTP error ${response.status}`);
            }

            // Try to parse the response, but don't fail if it's not valid JSON
            return response.json().catch(err => {
              console.warn('Could not parse JSON response, but proceeding anyway:', err);
              return { success: true };
            });
          })
          .then(result => {
            // Reload components to get the updated list
            this.loadUnlayerComponents();

            // Update status
            this.$emit('status-update', 'success', 'Component override removed successfully.');
          })
          .catch(error => {
            console.error('Error removing component override:', error);
            this.$emit('status-update', 'error', 'Failed to remove component override: ' + (error.message || 'Unknown error'));
          });
        }
      } else {
        // Open edit modal to create an override
        this.editComponentOverride(component);
      }
    },
    editComponentOverride(component) {
      // Find if this component is already overridden (has an override with overrideId matching this component's id)
      const overriddenComponent = this.globalEmailComponents.find(comp =>
        comp.overrideId === component.id &&
        comp.orgId === this.orgId
      );

      if (overriddenComponent) {
        // If overridden, use the overridden component data
        this.currentComponent = { ...overriddenComponent };
        console.log('Editing existing override:', this.currentComponent);
      } else {
        // Otherwise, use the original component data
        this.currentComponent = { ...component };
        console.log('Creating new override of component:', this.currentComponent);
      }

      this.componentMode = 'override';
      this.editingComponent = overriddenComponent ? true : false; // Only true if editing an existing override
      this.showComponentModal = true;
    },
    openComponentModal(mode, component = null) {
      this.componentMode = mode;
      this.showComponentModal = true;

      // Reset the form if not editing
      if (!component) {
        this.editingComponent = false;
        this.currentComponent = {
          id: '',
          name: '',
          description: '',
          json: '',
          editableFields: {},
          active: true
        };
      } else {
        // Set up for editing
        this.editingComponent = true;
        this.currentComponent = { ...component };
      }
    },
    closeComponentModal() {
      this.showComponentModal = false;
      this.currentComponent = {
        id: '',
        name: '',
        description: '',
        json: '',
        editableFields: {}
      };
      this.editingComponent = false;
    },
    saveComponentOverride(componentData) {
      try {
        // Prepare the component data for saving
        const dataToSave = {
          name: componentData.name,
          description: componentData.description || '', // Include AI Instructions
          json: componentData.json,
          type: 'unlayer',
          editableFields: JSON.stringify(componentData.editableFields), // Stringify for API
          orgId: this.orgId,
          active: componentData.active !== false // Default to true if not explicitly set to false
        };

        // When overriding a component, set the overrideId to the original component's ID
        if (this.componentMode === 'override') {
          if (!this.editingComponent) {
            // We're creating a new override of a global component
            // Set overrideId to the ID of the global component being overridden
            dataToSave.overrideId = componentData.id;
            console.log('Setting overrideId to', componentData.id, 'for new override');
          } else if (componentData.overrideId) {
            // We're editing an existing override, preserve its overrideId
            dataToSave.overrideId = componentData.overrideId;
            console.log('Preserving existing overrideId', componentData.overrideId);
          }
        }

        // If we're editing an existing component, include its ID
        if (this.editingComponent && componentData.id) {
          // Always include the ID when editing an existing component
          dataToSave.id = componentData.id;
          console.log('Including ID for existing component:', dataToSave.id);
        }

        console.log('Component data to save:', dataToSave);

        // Save the component to the server via API
        const url = `${URL_DOMAIN}/unlayer-components`;

        // Use PATCH if we're editing an existing component (has an ID)
        // Otherwise, use POST to create a new component
        const method = (this.editingComponent && dataToSave.id) ? 'PATCH' : 'POST';
        const finalUrl = (method === 'PATCH' && dataToSave.id) ? `${url}/${dataToSave.id}` : url;

        console.log(`Using ${method} method to ${method === 'PATCH' ? 'update' : 'create'} component at ${finalUrl}`);

        fetch(finalUrl, {
          method: method,
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${localStorage.getItem('token')}`
          },
          body: JSON.stringify(dataToSave)
        })
        .then(response => {
          if (!response.ok) {
            throw new Error(`HTTP error ${response.status}`);
          }

          // For PATCH requests, the response might be empty or not valid JSON
          if (method === 'PATCH') {
            // Just check if the status is OK and proceed
            return { success: true };
          } else {
            // For POST requests, try to parse the JSON response
            return response.json().catch(err => {
              console.warn('Could not parse JSON response, but proceeding anyway:', err);
              return { success: true };
            });
          }
        })
        .then(savedComponent => {
          console.log('Component saved successfully:', savedComponent);

          // Reload components to get the updated list
          this.loadUnlayerComponents();

          // Close the modal
          this.closeComponentModal();

          // Show success message
          const actionText = this.editingComponent ? 'updated' :
                           (this.componentMode === 'create' ? 'created' : 'customized');

          this.$emit('status-update', 'success', `Component ${actionText} successfully.`);
        })
        .catch(error => {
          console.error('Error saving component to server:', error);
          this.$emit('status-update', 'error', 'Failed to save component: ' + (error.message || 'Unknown error'));
        });
      } catch (error) {
        console.error('Error preparing component data:', error);
        this.$emit('status-update', 'error', 'Failed to prepare component data: ' + (error.message || 'Unknown error'));
      }
    },
    getOverrideComponent(componentId) {
      return this.globalEmailComponents.find(comp =>
        comp.overrideId === componentId &&
        comp.orgId === this.orgId
      );
    },
    deleteComponent(componentId) {
      if (!componentId) {
        return;
      }

      // Find the component to delete
      const component = this.globalEmailComponents.find(c => c.id === componentId);

      if (component) {
        // Only allow deleting components that belong to this organization
        if (component.orgId === this.orgId) {
          // Confirm before deleting
          if (confirm(`Are you sure you want to delete this component${component.name ? ` (${component.name})` : ''}?`)) {
            // Delete the component via API
            const url = new URL(`${URL_DOMAIN}/unlayer-components/${componentId}`);
            url.searchParams.append('orgId', this.orgId.toString());

            fetch(url.toString(), {
              method: 'DELETE',
              headers: {
                'Authorization': `Bearer ${localStorage.getItem('token')}`
              }
            })
            .then(response => {
              if (!response.ok) {
                throw new Error(`HTTP error ${response.status}`);
              }

              // Try to parse the response, but don't fail if it's not valid JSON
              return response.json().catch(err => {
                console.warn('Could not parse JSON response, but proceeding anyway:', err);
                return { success: true };
              });
            })
            .then(result => {
              // Reload components to get the updated list
              this.loadUnlayerComponents();

              // Update status
              this.$emit('status-update', 'success', 'Component deleted successfully.');
            })
            .catch(error => {
              console.error('Error deleting component:', error);
              this.$emit('status-update', 'error', 'Failed to delete component: ' + (error.message || 'Unknown error'));
            });
          }
        } else {
          this.$emit('status-update', 'error', 'You can only delete components that belong to your organization.');
        }
      }
    },
    onComponentSelect(componentId) {
      // Find the selected component
      if (!componentId) {
        return;
      }

      // Find the selected component in global components
      const selectedComponent = this.globalEmailComponents.find(
        (comp) => comp.id === componentId
      );

      if (selectedComponent) {
        // Set the JSON from the selected component
        this.currentComponent.json = selectedComponent.json;

        // Check if we already have an override for this component
        const existingOverride = this.globalEmailComponents.find(comp =>
          comp.id === selectedComponent.id &&
          comp.overrideId === this.overrideId &&
          comp.orgId === this.orgId
        );

        if (existingOverride) {
          // If we have an override, use its data instead
          this.currentComponent = { ...existingOverride };
        } else {
          // Otherwise, set the name and description from the selected component
          this.currentComponent.name = selectedComponent.name;
          this.currentComponent.description = selectedComponent.description || '';
        }
      }
    },
    createDeactivatedCopy(globalComponent) {
      console.log('Creating deactivated copy of global component:', globalComponent);

      // Process editableFields to ensure it's a string
      let editableFields = globalComponent.editableFields;
      if (editableFields) {
        if (typeof editableFields !== 'string') {
          // If it's not a string, stringify it
          editableFields = JSON.stringify(editableFields);
        }
      } else {
        // If it's undefined or null, set it to an empty object string
        editableFields = '{}';
      }

      // Validate JSON to ensure it's valid
      let json = globalComponent.json;
      try {
        // Try to parse and re-stringify to ensure it's valid JSON
        JSON.parse(json);
      } catch (e) {
        console.error('Invalid JSON in global component:', e);
        // If it's invalid, set it to an empty object
        json = '{}';
      }

      // Create a copy of the global component with active=false for this organization
      const componentData = {
        name: globalComponent.name,
        description: globalComponent.description || '', // Include description field
        json: json, // Use the validated JSON
        type: globalComponent.type || 'unlayer',
        editableFields: editableFields,
        orgId: this.orgId,
        overrideId: globalComponent.id, // Set the overrideId to the global component's ID
        active: false // Set active to false
      };

      console.log('Component data for deactivated copy:', componentData);

      // Save the new component to the server
      fetch(`${URL_DOMAIN}/unlayer-components`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify(componentData)
      })
      .then(async response => {
        if (!response.ok) {
          // Try to get more details about the error
          let errorDetails = '';
          try {
            const errorResponse = await response.json();
            console.error('Error response details:', errorResponse);
            if (errorResponse.error && errorResponse.error.message) {
              errorDetails = ': ' + errorResponse.error.message;
            }
          } catch (e) {
            // If we can't parse the error response, just use the status code
          }
          throw new Error(`HTTP error ${response.status}${errorDetails}`);
        }

        // Try to parse the response, but don't fail if it's not valid JSON
        return response.json().catch(err => {
          console.warn('Could not parse JSON response, but proceeding anyway:', err);
          return { success: true };
        });
      })
      .then(result => {
        // Reload components to get the updated list
        this.loadUnlayerComponents();

        // Show success message
        this.$emit('status-update', 'success', `Global component deactivated for this organization.`);
      })
      .catch(error => {
        console.error('Error creating deactivated copy:', error);

        // Provide a more detailed error message
        let errorMessage = 'Failed to deactivate global component';

        if (error.message) {
          errorMessage += ': ' + error.message;

          // Add specific guidance for common errors
          if (error.message.includes('422')) {
            errorMessage += '\n\nThis may be due to validation errors in the component data. Check the browser console for more details.';
          }
        }

        this.$emit('status-update', 'error', errorMessage);
      });
    },
    loadUnlayerComponents() {
      console.log('Loading Unlayer components...');
      // Set loading state
      this.loading = true;

      // Build the URL with query parameters for orgId and overrideId
      const url = new URL(`${URL_DOMAIN}/unlayer-components`);

      // Add filter to get both global components and organization-specific components
      // Using a simpler filter structure to avoid SQL syntax errors
      const filter = {
        where: {
          or: [
            { orgId: null }, // Global components (null instead of exists:false)
            { orgId: this.orgId } // Organization-specific components
          ]
        }
      };

      url.searchParams.append('filter', JSON.stringify(filter));

      // Fetch components
      fetch(url.toString(), {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      })
      .then(response => response.json())
      .then(data => {
        console.log('Loaded components:', data);
        if (data && Array.isArray(data)) {
          this.globalEmailComponents = data.map(comp => {
            // Ensure editableFields is properly formatted
            if (comp.editableFields && typeof comp.editableFields === 'string') {
              try {
                const fields = JSON.parse(comp.editableFields);
                console.log('Successfully parsed editableFields for component:', comp.name, fields);
                comp.editableFields = fields;
              } catch (e) {
                console.error('Error parsing component editable fields:', e);
                comp.editableFields = {};
              }
            } else if (!comp.editableFields) {
              console.log('No editableFields found for component', comp.name, 'initializing empty object');
              comp.editableFields = {};
            } else {
              console.log('Component', comp.name, 'already has object editableFields:', comp.editableFields);
            }
            return comp;
          });
        }
        this.loading = false;
      })
      .catch(error => {
        console.error('Error loading components:', error);
        this.$emit('status-update', 'error', 'Failed to load components');
        this.loading = false;
      });
    },
    debugComponent(component) {
      console.log('Debugging component:', component.name, '(ID:', component.id, ')');

      // Check editable fields in various formats
      console.log('editableFields raw value:', component.editableFields);

      // Parse if it's a string
      let parsedFields = component.editableFields;
      if (typeof component.editableFields === 'string') {
        try {
          parsedFields = JSON.parse(component.editableFields);
          console.log('editableFields parsed from string:', parsedFields);
        } catch (e) {
          console.error('Failed to parse editableFields string:', e);
        }
      } else if (typeof component.editableFields === 'object') {
        parsedFields = component.editableFields;
        console.log('editableFields is already an object');
      }

      // Count the editable fields
      const fieldCount = parsedFields ? Object.keys(parsedFields).length : 0;

      // Format the component details for display
      let details = `Component: ${component.name}\n`;
      details += `ID: ${component.id}\n`;
      details += `Type: ${component.type || 'unlayer'}\n`;
      details += `Active: ${component.active !== false ? 'Yes' : 'No'}\n`;
      details += `AI Instructions: ${component.description || '(none)'}\n\n`;
      details += `Editable Fields Count: ${fieldCount}\n\n`;

      if (fieldCount > 0) {
        details += `Editable Fields:\n`;
        details += `---------------\n`;
        Object.entries(parsedFields).forEach(([path, description]) => {
          details += `${path}: ${description || '(no description)'}\n`;
        });
      } else {
        details += `No editable fields found.`;
      }

      // Display in alert for immediate feedback
      alert(details);

      // Also check if this is an override component
      if (component.orgId === this.orgId && component.overrideId === this.overrideId) {
        console.log('This is an override component for this organization and override ID');
        console.log('Override editableFields:', component.editableFields);
      }

      return details;
    }
  },
  computed: {
    // Get all components for display
    allComponents() {
      // Get all override components for this organization
      const overrideComponents = this.globalEmailComponents.filter(comp =>
        comp.orgId === this.orgId && comp.overrideId !== null && comp.overrideId !== undefined
      );

      // Create a map of overrideId -> override component for quick lookup
      const overrideMap = {};
      overrideComponents.forEach(comp => {
        if (comp.overrideId) {
          overrideMap[comp.overrideId] = comp;
        }
      });

      // Get global components that don't have overrides
      const globalWithoutOverrides = this.globalEmailComponents.filter(comp => {
        // Include only global components (no orgId)
        const isGlobal = comp.orgId === null || comp.orgId === undefined;

        // Exclude if there's an override for this component
        const hasOverride = overrideMap[comp.id] !== undefined;

        return isGlobal && !hasOverride;
      });

      // Get organization-specific components that are not overrides
      const orgSpecificComponents = this.globalEmailComponents.filter(comp => {
        return comp.orgId === this.orgId &&
               (comp.overrideId === null || comp.overrideId === undefined);
      });

      // Process global components with overrides
      const processedComponents = [];

      // Add global components without overrides
      globalWithoutOverrides.forEach(comp => {
        processedComponents.push(comp);
      });

      // Add organization-specific components
      orgSpecificComponents.forEach(comp => {
        processedComponents.push(comp);
      });

      // For each override component, check if it's a deactivated global component
      overrideComponents.forEach(override => {
        // Find the original global component
        const originalGlobal = this.globalEmailComponents.find(comp =>
          comp.id === override.overrideId &&
          (comp.orgId === null || comp.orgId === undefined)
        );

        if (originalGlobal) {
          // This is an override of a global component
          // Replace the global component with the override in the list
          processedComponents.push(override);
        } else {
          // This is an override of something else or the original doesn't exist anymore
          processedComponents.push(override);
        }
      });

      // Filter out inactive components unless showInactive is true
      const activeComponents = processedComponents.filter(comp => {
        // If active is undefined, treat as active (for backward compatibility)
        const isActive = comp.active === undefined || comp.active === true;
        return this.showInactive || isActive;
      });

      return activeComponents;
    }
  }
};
</script>

<style scoped>
/* Add any specific styles for the component overrides section */
pre {
  white-space: pre-wrap;
  word-break: break-word;
  max-height: 300px;
  overflow-y: auto;
}
</style>
