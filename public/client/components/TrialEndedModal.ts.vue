<template>
  <!-- Modal backdrop - No click to close -->
  <transition
    enter-active-class="transition ease-out duration-200"
    enter-from-class="opacity-0"
    enter-to-class="opacity-100"
    leave-active-class="transition ease-out duration-100"
    leave-from-class="opacity-100"
    leave-to-class="opacity-0"
  >
    <div v-show="show" class="fixed inset-0 bg-slate-900 bg-opacity-90 z-[99999] transition-opacity backdrop-blur-sm" aria-hidden="true"></div>
  </transition>

  <!-- Modal dialog - No dismiss options -->
  <transition
    enter-active-class="transition ease-in-out duration-200"
    enter-from-class="opacity-0 translate-y-4"
    enter-to-class="opacity-100 translate-y-0"
    leave-active-class="transition ease-in-out duration-200"
    leave-from-class="opacity-100 translate-y-0"
    leave-to-class="opacity-0 translate-y-4"
  >
    <div v-show="show" class="fixed inset-0 z-[100000] overflow-hidden flex items-center my-4 justify-center transform px-4 sm:px-6" role="dialog" aria-modal="true">
      <div class="trial-ended-modal" @click.stop>
        <!-- Header -->
        <div class="modal-header">
          <div class="header-content">
            <div class="info-icon">
              <svg width="28" height="28" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2"/>
                <path d="M12 16v-4M12 8h.01" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </div>
            <div class="header-text">
              <h2 class="modal-title">Trial Complete</h2>
              <p class="modal-subtitle">Continue your AI marketing journey</p>
            </div>
          </div>
          <!-- No close button - blocking modal -->
        </div>

        <!-- Message -->
        <div class="message-section">
          <div class="message-content">
            <h3 class="message-title">Ready to Level Up?</h3>
            <p class="message-description">
              Your 14-day trial has completed! Keep your existing functionality and continue accessing
              Raleon's powerful AI marketing tools with our affordable starter plan.
            </p>
            <div class="continuity-message">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="shield-icon">
                <path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M9 12l2 2 4-4" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
              <span>All your data, integrations, and settings are preserved</span>
            </div>
          </div>
        </div>

        <!-- Upgrade Section -->
        <div class="upgrade-section">
          <div class="upgrade-header">
            <span class="crown-icon">⚡</span>
            <span class="upgrade-title">{{ planTitle }}</span>
            <div class="recommended-badge">{{ planBadge }}</div>
          </div>
          <div class="features-list">
            <div v-for="(feature, index) in planFeatures" :key="index" class="feature-item">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="check-icon">
                <path d="M20 6L9 17l-5-5" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
              <span>{{ feature }}</span>
            </div>
          </div>
          <div class="pricing-section">
            <div class="pricing-main">
              <span class="price">{{ planPrice }}</span>
              <span class="period">/month</span>
            </div>
            <div class="pricing-subtitle">{{ planDescription }}</div>
          </div>
          <button class="upgrade-button" @click="navigateToUpgrade">
            <span>Upgrade Now</span>
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="arrow-icon">
              <path d="M7 17L17 7M17 7H7M17 7V17" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
          </button>
        </div>

        <!-- Additional Options -->
        <div class="additional-options">
          <p class="options-text">
            Need help choosing a plan? <a href="mailto:<EMAIL>" class="support-link">Contact our team</a>
          </p>
        </div>
      </div>
    </div>
  </transition>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from '@vue/runtime-core';
import { useRouter } from 'vue-router';
import { URL_DOMAIN } from '../utils/utils';

const props = defineProps<{
  show: boolean;
}>();

// No emit for close - this is a blocking modal
const router = useRouter();

const integrationStatus = ref({
  shopify: false,
  klaviyo: false
});

// Check integration status
const hasOneIntegration = computed(() => {
  return (integrationStatus.value.shopify || integrationStatus.value.klaviyo) &&
         !(integrationStatus.value.shopify && integrationStatus.value.klaviyo);
});

const hasBothIntegrations = computed(() => {
  return integrationStatus.value.shopify && integrationStatus.value.klaviyo;
});

// Determine which plan to show
const planTitle = computed(() => {
  if (hasBothIntegrations.value) {
    return 'Strategist Max';
  } else if (hasOneIntegration.value) {
    return 'Strategist';
  } else {
    return 'Strategist Lite';
  }
});

const planPrice = computed(() => {
  if (hasBothIntegrations.value) {
    return '$199';
  } else if (hasOneIntegration.value) {
    return '$99';
  } else {
    return '$20';
  }
});

const planFeatures = computed(() => {
  if (hasBothIntegrations.value) {
    return [
      '50 AI messages per day',
      'AI Segmentation',
      'Dedicated Slack support',
      'Klaviyo and Shopify Integrations',
      'Access to AI Knowledge',
      'Early access to Email generation'
    ];
  } else if (hasOneIntegration.value) {
    return [
      '30 AI messages per day',
      'Klaviyo and Shopify Integrations',
      'Access to AI Knowledge',
      'Early access to Email generation'
    ];
  } else {
    return [
      'Perfect for 1 brand',
      '20 AI messages per day',
      'Access to AI Knowledge',
      'Email Brief Creation'
    ];
  }
});

const planDescription = computed(() => {
  if (hasBothIntegrations.value) {
    return 'For power users with full integration';
  } else if (hasOneIntegration.value) {
    return 'Perfect for established brands';
  } else {
    return 'Great value for growing brands';
  }
});

const planBadge = computed(() => {
  if (hasBothIntegrations.value) {
    return 'Most Popular';
  } else if (hasOneIntegration.value) {
    return 'Recommended';
  } else {
    return 'Perfect Start';
  }
});

async function checkIntegrations() {
  const token = localStorage.getItem('token');
  try {
    // Check Shopify connection
    const shopifyResponse = await fetch(`${URL_DOMAIN}/integration/shopify/connected`, {
      headers: {'Authorization': `Bearer ${token}`}
    });
    if (shopifyResponse.ok) {
      const shopifyData = await shopifyResponse.json();
      integrationStatus.value.shopify = shopifyData.connected;
    }

    // Check Klaviyo connection
    const klaviyoResponse = await fetch(`${URL_DOMAIN}/integration/klaviyo/connected`, {
      headers: {'Authorization': `Bearer ${token}`}
    });
    if (klaviyoResponse.ok) {
      const klaviyoData = await klaviyoResponse.json();
      integrationStatus.value.klaviyo = klaviyoData.connected;
    }
  } catch (error) {
    console.error('Error checking integrations:', error);
  }
}

function navigateToUpgrade() {
  // Navigate to billing upgrade page
  router.push('/loyalty/settings/plans');
}

onMounted(() => {
  checkIntegrations();
});
</script>

<style scoped>
/* Main Modal */
.trial-ended-modal {
  background: #ffffff;
  border-radius: 16px;
  width: 90%;
  max-width: 520px;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25), 0 20px 25px -5px rgba(0, 0, 0, 0.1);
  position: relative;
  border: 2px solid #e5e7eb;
}

/* Header */
.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24px 24px 16px;
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  border-bottom: 1px solid #bae6fd;
  border-radius: 14px 14px 0 0;
}

.header-content {
  display: flex;
  align-items: center;
  gap: 16px;
  flex: 1;
}

.info-icon {
  background: #0ea5e9;
  border-radius: 12px;
  padding: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  flex-shrink: 0;
}

.header-text {
  flex: 1;
}

.modal-title {
  font-size: 24px;
  font-weight: 700;
  color: #1f2937;
  margin: 0;
  font-family: 'Inter', sans-serif;
}

.modal-subtitle {
  font-size: 16px;
  color: #6b7280;
  margin: 4px 0 0 0;
  font-family: 'Inter', sans-serif;
}

/* Message Section */
.message-section {
  padding: 24px;
  background: white;
  border-bottom: 1px solid #f3f4f6;
}

.message-content {
  text-align: center;
}

.message-title {
  font-size: 18px;
  font-weight: 600;
  color: #374151;
  margin: 0 0 12px 0;
  font-family: 'Inter', sans-serif;
}

.message-description {
  font-size: 15px;
  color: #6b7280;
  line-height: 1.6;
  margin: 0 0 16px 0;
  font-family: 'Inter', sans-serif;
}

.continuity-message {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  border: 1px solid #bae6fd;
  border-radius: 8px;
  font-size: 14px;
  color: #0369a1;
  font-weight: 500;
  font-family: 'Inter', sans-serif;
}

.shield-icon {
  color: #0ea5e9;
  flex-shrink: 0;
}

/* Upgrade Section */
.upgrade-section {
  background: linear-gradient(135deg, #1e1b4b 0%, #312e81 100%);
  color: white;
  padding: 24px;
  position: relative;
  overflow: hidden;
}

.upgrade-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(139, 92, 246, 0.1) 0%, rgba(168, 85, 247, 0.1) 100%);
  pointer-events: none;
}

.upgrade-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 20px;
  position: relative;
  z-index: 1;
}

.crown-icon {
  font-size: 20px;
}

.upgrade-title {
  font-size: 20px;
  font-weight: 700;
  font-family: 'Inter', sans-serif;
  flex: 1;
}

.recommended-badge {
  background: rgba(59, 130, 246, 0.9);
  color: white;
  padding: 4px 12px;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 600;
  font-family: 'Inter', sans-serif;
}

.features-list {
  margin-bottom: 24px;
  position: relative;
  z-index: 1;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
  font-size: 15px;
  font-family: 'Inter', sans-serif;
}

.feature-item:last-child {
  margin-bottom: 0;
}

.check-icon {
  color: #3b82f6;
  flex-shrink: 0;
}

.pricing-section {
  text-align: center;
  margin-bottom: 24px;
  position: relative;
  z-index: 1;
}

.pricing-main {
  display: flex;
  align-items: baseline;
  justify-content: center;
  gap: 4px;
  margin-bottom: 6px;
}

.price {
  font-size: 36px;
  font-weight: 800;
  font-family: 'Inter', sans-serif;
}

.period {
  font-size: 18px;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.8);
  font-family: 'Inter', sans-serif;
}

.pricing-subtitle {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.7);
  font-family: 'Inter', sans-serif;
}

.upgrade-button {
  background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
  color: white;
  border: none;
  padding: 16px 32px;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  font-family: 'Inter', sans-serif;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  position: relative;
  z-index: 1;
  box-shadow: 0 4px 14px 0 rgba(59, 130, 246, 0.3);
}

.upgrade-button:hover {
  background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px 0 rgba(59, 130, 246, 0.4);
}

.upgrade-button:active {
  transform: translateY(0);
}

.arrow-icon {
  transition: transform 0.3s ease;
}

.upgrade-button:hover .arrow-icon {
  transform: translate(2px, -2px);
}

/* Additional Options */
.additional-options {
  padding: 20px 24px 24px;
  background: #f9fafb;
  text-align: center;
  border-radius: 0 0 14px 14px;
}

.options-text {
  font-size: 14px;
  color: #6b7280;
  margin: 0;
  font-family: 'Inter', sans-serif;
}

.support-link {
  color: #6366f1;
  text-decoration: none;
  font-weight: 500;
  transition: color 0.2s ease;
}

.support-link:hover {
  color: #4f46e5;
  text-decoration: underline;
}

/* Responsive */
@media (max-width: 768px) {
  .trial-ended-modal {
    width: 95%;
    margin: 20px;
  }

  .modal-title {
    font-size: 20px;
  }

  .modal-subtitle {
    font-size: 14px;
  }

  .price {
    font-size: 32px;
  }

  .upgrade-button {
    padding: 14px 24px;
    font-size: 15px;
  }
}

/* Prevent text selection on the entire modal */
.trial-ended-modal {
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

/* Prevent right-click context menu */
.trial-ended-modal {
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}
</style>
