<template>
  <div class="mobile-sidebar">
    <!-- Mobile menu button -->
    <button @click="toggleMobileMenu" class="mobile-menu-button">
      <svg v-if="!isMobileMenuOpen" width="32px" height="32px" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M20 7L4 7" stroke="#1C274C" stroke-width="1.5" stroke-linecap="round" />
        <path d="M20 12L4 12" stroke="#1C274C" stroke-width="1.5" stroke-linecap="round" />
        <path d="M20 17L4 17" stroke="#1C274C" stroke-width="1.5" stroke-linecap="round" />
      </svg>
      <svg v-else xmlns="http://www.w3.org/2000/svg" class="close-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
        <line x1="18" y1="6" x2="6" y2="18"></line>
        <line x1="6" y1="6" x2="18" y2="18"></line>
      </svg>
    </button>

    <!-- Mobile sidebar -->
    <transition name="slide">
      <div v-if="isMobileMenuOpen" class="sidebar-content">
        <nav>
          <ul class="main-menu">
            <li>
              <button @click="navigateToChat(); closeMobileMenu()" class="menu-item">New Chat</button>
            </li>
            <li v-for="item in navigationItems" :key="item.name">
              <router-link
                v-if="!item.expandable"
                @click="closeMobileMenu"
                :to="item.path"
                class="menu-item"
              >
                {{ item.label }}
              </router-link>
              <div v-else class="menu-item" @click="toggleMoreTools">
                {{ item.label }}
              </div>
              <ul v-if="item.expandable && moreToolsExpanded" class="sub-menu">
                <li v-for="subItem in moreToolsItems" :key="subItem.name">
                  <router-link
                    v-if="!subItem.expandable"
                    @click="closeMobileMenu"
                    :to="subItem.path"
                    class="sub-menu-item"
                  >
                    {{ subItem.label }}
                  </router-link>
                  <div v-else class="sub-menu-item" @click.stop="toggleLoyalty">
                    {{ subItem.label }}
                  </div>
                  <ul v-if="subItem.expandable && loyaltyExpanded" class="sub-sub-menu">
                    <li v-for="loyaltyItem in loyaltyItems" :key="loyaltyItem.name">
                      <router-link @click="closeMobileMenu" :to="loyaltyItem.path" class="sub-sub-menu-item">
                        {{ loyaltyItem.label }}
                      </router-link>
                    </li>
                  </ul>
                </li>
              </ul>
            </li>
          </ul>

          <ul class="main-menu">
            <li v-for="item in bottomItems" :key="item.name">
              <template v-if="!item.condition || item.condition()">
                <button v-if="item.isButton" @click="item.action" class="menu-item">{{ item.label }}</button>
                <component
                  v-else
                  :is="item.external ? 'a' : 'router-link'"
                  :to="!item.external && item.path"
                  :href="item.external && item.path"
                  :target="item.external ? '_blank' : null"
                  :rel="item.external ? 'noopener noreferrer' : null"
                  class="menu-item"
                  @click="!item.external && closeMobileMenu()"
                >
                  {{ item.label }}
                </component>
              </template>
            </li>
          </ul>
        </nav>
      </div>
    </transition>
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import * as Utils from '../../client-old/utils/Utils';
import { isFeatureAvailable } from '../services/features.js';
import {customerIOTrackEvent} from '../services/customerio.js';

const URL_DOMAIN = Utils.URL_DOMAIN;
const PLAN_IDS = Utils.PLAN_IDS;

export default {
  name: 'MobileSidebar',
  setup() {
    const router = useRouter();
    const isMobileMenuOpen = ref(false);
    const moreToolsExpanded = ref(false);
    const loyaltyExpanded = ref(false);
    const dev_doc_url = ref('https://docs.raleon.io');
    const shopifyConnected = ref(true);
    const showQuotaModal = ref(false);

    const moreToolsItems = ref([
      { name: 'giftWithPurchase', path: '/promotions/gift-with-purchase', routeMatch: '/promotions/gift-with-purchase', label: 'Gift With Purchase' },
    ]);

    const loyaltyItems = ref([
      { name: 'getStarted', path: '/loyalty/quickstart', routeMatch: '/loyalty/quickstart', label: 'Get Started' },
      { name: 'customers', path: '/loyalty/customers', routeMatch: '/loyalty/customers', label: 'Customers' },
      { name: 'points', path: '/loyalty/program', routeMatch: '/loyalty/program', label: 'Points' },
      { name: 'vip', path: '/loyalty/program/vip', routeMatch: '/loyalty/program/vip', label: 'VIP' },
      { name: 'referrals', path: '/loyalty/program/referrals', routeMatch: '/loyalty/program/referrals', label: 'Referrals' },
      { name: 'branding', path: '/loyalty/onsite', routeMatch: '/loyalty/onsite', label: 'Branding' },
      { name: 'campaigns', path: '/loyalty/campaigns', routeMatch: '/loyalty/campaigns', label: 'Campaigns' },
      { name: 'analytics', path: '/loyalty/analytics', routeMatch: '/loyalty/analytics', label: 'Analytics' },
      { name: 'communication', path: '/loyalty/communication', routeMatch: '/loyalty/communication', label: 'Communication' },
    ]);

    const baseNavigationItems = [
      { name: 'chat', path: '/chats', routeMatch: '/chat', label: 'Chats' },
      { name: 'knowledge', path: '/ai-strategist/knowledge', routeMatch: '/ai-strategist/knowledge', label: 'Knowledge' },
      { name: 'commandCenter', path: '/ai-strategist/planning', routeMatch: '/ai-strategist/planning', label: 'Command Center' },
      { name: 'calendar', path: '/ai-strategist/tasks', routeMatch: '/ai-strategist/tasks', label: 'Calendar' },
      { name: 'segments', path: '/ai-segments/overview', routeMatch: '/ai-segments', label: 'Segments' },
      { name: 'analytics', path: '/ai-strategist/analytics', routeMatch: '/ai-strategist/analytics', label: 'Analytics' },
      { name: 'integrations', path: '/integrations', routeMatch: '/integrations', label: 'Integrations' },
      { name: 'moreTools', routeMatch: '/more-tools', label: 'More Tools', expandable: true },
    ];

    const shouldShowMoreTools = computed(() => {
      try {
        const featureStates = JSON.parse(localStorage.getItem('featureStates') || '{}');
        const planId = featureStates?.plan?.id;
        return planId && planId >= PLAN_IDS.LEGACY_START && planId <= PLAN_IDS.LEGACY_END;
      } catch (error) {
        console.error('Error checking plan for More Tools visibility:', error);
        return false;
      }
    });

    const shouldShowManageBrands = computed(() => {
      try {
        const featureStates = JSON.parse(localStorage.getItem('featureStates') || '{}');
        const planId = featureStates?.plan?.id;
        return planId === PLAN_IDS.AGENCY_PLATFORM;
      } catch (error) {
        console.error('Error checking plan for Manage Brands visibility:', error);
        return false;
      }
    });

    const navigationItems = computed(() => {
      return baseNavigationItems.filter(item => {
        if (item.name === 'moreTools' && !shouldShowMoreTools.value) {
          return false;
        }
        return true;
      });
    });

    const bottomItems = ref([
      { name: 'settings', path: '/settings', routeMatch: '/settings', label: 'Settings' },
      { name: 'brands', path: '/agency', routeMatch: '/agency', label: 'Manage Brands', condition: () => shouldShowManageBrands.value },
      { name: 'messages', label: 'Messages', isButton: true, action: () => { showQuotaModal.value = true; } },
      { name: 'help', path: dev_doc_url, external: true, label: 'Help' },
      { name: 'billing', path: '/loyalty/settings/plans', routeMatch: '/loyalty/settings/plans', label: 'Billing' },
      { name: 'signOut', label: 'Sign Out', isButton: true, action: () => { localStorage.removeItem('token'); localStorage.removeItem('userInfo'); router.push('/signin'); closeMobileMenu(); } },
    ]);

    const checkShopifyConnection = async () => {
      try {
        const response = await fetch(`${URL_DOMAIN}/integration/shopify/connected`, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${localStorage.getItem('token')}`,
          },
        });
        if (!response.ok) throw new Error('Failed to check Shopify connection');
        const data = await response.json();
        shopifyConnected.value = data.connected;
      } catch (error) {
        console.error('Error checking Shopify connection:', error);
        shopifyConnected.value = false;
      }
    };

    const isLoyaltyAvailable = computed(() => isFeatureAvailable('points') && shopifyConnected.value);

    onMounted(async () => {
      customerIOTrackEvent('Mobile Device User');
      await checkShopifyConnection();
      const loyaltyItem = {
        name: 'loyalty',
        path: '/loyalty/program',
        routeMatch: '/loyalty',
        label: 'Loyalty',
        expandable: isLoyaltyAvailable.value,
      };
      moreToolsItems.value.unshift(loyaltyItem);

      try {
        const docLoginResponse = await fetch(`${URL_DOMAIN}/users/doc-login`, {
          method: 'GET',
          credentials: 'omit',
          mode: 'cors',
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`,
            'Access-Control-Allow-Origin': '*',
            'Content-Type': 'application/json',
          },
        });
        const docData = await docLoginResponse.json();
        if (docData.docs_url) dev_doc_url.value = docData.docs_url;
        bottomItems.value = bottomItems.value.map(item => item.name === 'help' ? { ...item, path: dev_doc_url.value } : item);
      } catch (err) {
        console.error('Error fetching docs URL:', err);
      }
    });

    const toggleMobileMenu = () => { isMobileMenuOpen.value = !isMobileMenuOpen.value; };
    const closeMobileMenu = () => { isMobileMenuOpen.value = false; moreToolsExpanded.value = false; loyaltyExpanded.value = false; };
    const toggleMoreTools = () => { moreToolsExpanded.value = !moreToolsExpanded.value; if (!moreToolsExpanded.value) loyaltyExpanded.value = false; };
    const toggleLoyalty = () => { loyaltyExpanded.value = !loyaltyExpanded.value; };
    const navigateToChat = () => { router.push('/chat'); };

    return {
      isMobileMenuOpen,
      dev_doc_url,
      loyaltyItems,
      moreToolsItems,
      navigationItems,
      bottomItems,
      moreToolsExpanded,
      loyaltyExpanded,
      toggleMobileMenu,
      toggleMoreTools,
      toggleLoyalty,
      closeMobileMenu,
      navigateToChat,
    };
  },
};
</script>

<style scoped>
.mobile-sidebar {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1000;
}

.mobile-menu-button {
  position: fixed;
  top: 1rem;
  left: 1rem;
  z-index: 1010;
  background: none;
  border: none;
  cursor: pointer;
}

.logo {
  height: 2rem;
  width: auto;
}

.close-icon {
  height: 2rem;
  width: 2rem;
  color: white;
}

.sidebar-content {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: #222141;
  color: white;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
}

.menu-container {
  flex-grow: 1;
  overflow-y: auto;
  padding: 1rem;
  padding-top: 4rem;
}

.main-menu {
  list-style-type: none;
  padding: 0;
  margin-left: 20px;
  margin-top: 40px;
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 0.75rem 1rem;
  font-size: 1.1rem;
  color: white;
  text-decoration: none;
  border-radius: 0.5rem;
  transition: background-color 0.3s;
}

.menu-item:hover {
  background-color: #32316A;
}

.sub-menu {
  list-style-type: none;
  padding-left: 1rem;
}

.sub-menu-item {
  display: block;
  padding: 0.5rem 1rem;
  color: #d1d5db;
  text-decoration: none;
  font-size: 0.9rem;
  border-radius: 0.5rem;
  transition: background-color 0.3s;
}

.sub-menu-item:hover {
  background-color: #32316A;
}

.icon {
  width: 1.5rem;
  height: 1.5rem;
  margin-right: 0.75rem;
}

.disabled {
  opacity: 0.5;
  pointer-events: none;
}

.slide-enter-active,
.slide-leave-active {
  transition: transform 0.3s ease;
}

.slide-enter-from,
.slide-leave-to {
  transform: translateX(-100%);
}

.sub-sub-menu {
  list-style-type: none;
  padding-left: 1rem;
}

.sub-sub-menu-item {
  display: block;
  padding: 0.5rem 1rem;
  color: #d1d5db;
  text-decoration: none;
  font-size: 0.8rem;
  border-radius: 0.5rem;
  transition: background-color 0.3s;
}

.sub-sub-menu-item:hover {
  background-color: #32316A;
}
</style>
