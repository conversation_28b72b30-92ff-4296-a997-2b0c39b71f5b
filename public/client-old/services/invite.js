import * as Utils from '../utils/Utils';

export async function getUsers() {
	let response;
	try {
		response = await fetch(`${Utils.URL_DOMAIN}/admin/org-users/`, {
			method: 'GET',
			withCreditentials: true,
			credentials: 'omit',
			headers: {
				'Authorization': `Bearer ${localStorage.getItem('token')}`,
				'Access-Control-Allow-Origin': '*',
				'Content-Type': 'application/json'
			}
		});
		response = await response.json();
	} catch (err) {
		console.log("Error: " + JSON.stringify(err));

	}
	return response;
 }

 export async function inviteUsers(emailsToInvite, selectedRole) {
	const roles = selectedRole == 'customer' ? ['customer'] : [selectedRole, 'customer'];
	const inviteRequests = emailsToInvite.map(email => {
		return {
			email: email,
			adminEmail: JSON.parse(localStorage.getItem('userInfo')).email,
			roles
		}
	});

	let response = await fetch(`${Utils.URL_DOMAIN}/admin/invite-users`, {
		method: 'POST',
		withCreditentials: true,
		credentials: 'omit',
		headers: {
			'Authorization': `Bearer ${localStorage.getItem('token')}`,
			'Access-Control-Allow-Origin': '*',
			'Content-Type': 'application/json'
		},
		body: JSON.stringify(inviteRequests)
	});
	response = await response.json();

	if (!response || response.error) {
		console.log("Error: " + JSON.stringify(response.error.message));
		throw new Error(response.error.message);
	}
	return response;
 }

 export async function resendInvite(email) {
	let response = await fetch(`${Utils.URL_DOMAIN}/admin/resend-invite`, {
		method: 'POST',
		withCreditentials: true,
		credentials: 'omit',
		headers: {
			'Authorization': `Bearer ${localStorage.getItem('token')}`,
			'Access-Control-Allow-Origin': '*',
			'Content-Type': 'application/json'
		},
		body: JSON.stringify({
			email: email,
			adminEmail: JSON.parse(localStorage.getItem('userInfo')).email,
		})
	});
	response = await response.json();
	if (!response || response.error) {
		console.log("Error: " + JSON.stringify(response.error.message));
		throw new Error(response.error.message);
	}
	return response;
}

export async function removeUser(userId) {
	let response = await fetch(`${Utils.URL_DOMAIN}/admin/remove-user/${userId}`, {
		method: 'DELETE',
		withCreditentials: true,
		credentials: 'omit',
		headers: {
			'Authorization': `Bearer ${localStorage.getItem('token')}`,
			'Access-Control-Allow-Origin': '*',
			'Content-Type': 'application/json'
		}
	});
	response = await response.json();
	if (!response || response.error) {
		console.log("Error: " + JSON.stringify(response.error.message));
		throw new Error(response.error.message);
	}
	return response;
}


 export async function acceptInvite(invite, isSecondaryAccount) {
	if (isSecondaryAccount) {
		if (!invite.email || !invite.inviteCode) {
			throw new Error('Invite is required');
		}
	} else {
		if (!invite.email || !invite.firstName || !invite.lastName || !invite.password || !invite.inviteCode) {
			throw new Error('Invite is required');
		}
	}
	let response = await fetch(`${Utils.URL_DOMAIN}/organization/accept-invite`, {
		method: 'POST',
		credentials: 'omit',
		headers: {
			'Access-Control-Allow-Origin': '*',
			'Content-Type': 'application/json'
		},
		body: JSON.stringify(invite)
	});
	response = await response.json();

	if (!response || response.error) {
		console.log("Error: " + JSON.stringify(response.error.message));
		throw new Error(response.error.message);
	}
	return response;
 }

 export async function selfService(invite) {
	if (!invite.email || !invite.password || !invite.organizationName) {
		throw new Error('Data is required');
	}
	let response = await fetch(`${Utils.URL_DOMAIN}/onboard/self-service`, {
		method: 'POST',
		credentials: 'omit',
		headers: {
			'Access-Control-Allow-Origin': '*',
			'Content-Type': 'application/json'
		},
		body: JSON.stringify(invite)
	});
	response = await response.json();

	if (!response || response.error) {
		console.log("Error: " + JSON.stringify(response.error.message));
		throw new Error(response.error.message);
	}
	return response;
 }
