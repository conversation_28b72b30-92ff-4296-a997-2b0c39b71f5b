import {Entity, model, property, belongsTo} from '@loopback/repository';
import {Campaign} from './campaign.model';
import {Segment} from './segment.model';

@model()
export class CampaignSegment extends Entity {
  @property({
    type: 'number',
    id: true,
    generated: true,
  })
  id?: number;

  @belongsTo(() => Campaign)
  campaignId: number;

  @belongsTo(() => Segment)
  segmentId: number;

  @property({
    type: 'string',
    required: false,
  })
  klaviyoSegmentId?: string;

  @property({
    type: 'string',
    required: false,
  })
  segmentType?: 'raleon' | 'klaviyo';

  constructor(data?: Partial<CampaignSegment>) {
    super(data);
  }
}

export interface CampaignSegmentRelations {
  // describe navigational properties here
  campaign: Campaign;
}

export type CampaignSegmentWithRelations = CampaignSegment & CampaignSegmentRelations;
