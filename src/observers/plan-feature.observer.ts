import {
  lifeCycleObserver,
  LifeCycleObserver,
} from '@loopback/core';
import {repository} from '@loopback/repository';
import {
  FeatureRepository,
  PlanRepository,
  PlanFeatureRepository,
  PlanRevenuePricingRepository
} from '../repositories';
import {Feature, Plan, PlanFeature, PlanRevenuePricing} from '../models';
import {ALifeCycleObserver} from './a-lifecycle-observer';

@lifeCycleObserver('FeatureManagement')
export class FeatureManagementObserver extends ALifeCycleObserver {
  constructor(
    @repository(FeatureRepository)
    public featureRepository: FeatureRepository,
    @repository(PlanRepository)
    public planRepository: PlanRepository,
    @repository(PlanFeatureRepository)
    public planFeatureRepository: PlanFeatureRepository,
    @repository(PlanRevenuePricingRepository)
    public planRevenuePricingRepository: PlanRevenuePricingRepository
  ) {
    super();
  }

  /**
   * This method will be invoked when the application starts.
   */
  async start(): Promise<void> {
	if (!this.shouldRun()) return;

    await this.populatePlans();
	await this.populatePlanRevenuePricing();
    await this.populateFeatures();
    await this.populatePlanFeatures();
  }

  /**
   * Populate the Plan model with predefined plans.
   */
  private async populatePlans(): Promise<void> {
    const plansData: Partial<Plan>[] = [
      {id: 1, name: 'Free', price: 0, description: 'Free plan for up to 250K', hasRevenueBasedPricing: true, active: true },
      {id: 2, name: 'GWP Only', price: -1, description: 'Gift With Purchase only plan', hasRevenueBasedPricing: true, active: false },
      {id: 3, name: 'Loyalty Growth', price: 49.00, description: 'Growth plan with loyalty features', hasRevenueBasedPricing: false, active: false },
      {id: 8, name: 'Free Loyalty + GWP', price: -1, description: 'Free plan with loyalty and GWP features', hasRevenueBasedPricing: true, active: false },
      {id: 4, name: 'Loyalty + GWP Growth', price: -1, description: 'Growth plan with loyalty and GWP features', hasRevenueBasedPricing: true, active: false },
      {id: 5, name: 'Loyalty Pro', price: -1, description: 'Pro plan with loyalty features', hasRevenueBasedPricing: true, active: false },
      {id: 6, name: 'Loyalty + GWP Pro', price: -1, description: 'Pro plan with loyalty and GWP features', hasRevenueBasedPricing: true, active: false },
      {id: 7, name: 'Legacy', price: -1, description: 'Legacy plan for pre-pricing customers', active: false },
	  {id: 9, name: 'Growth', price: -1, description: '+Free Feaures and', hasRevenueBasedPricing: true, active: true },
	  {id: 10, name: 'Pro', price: -1, description: '+Growth Features and', hasRevenueBasedPricing: true, active: true },
	  {id: 11, name: 'Enterprise', price: -1, description: '+Pro Features and', hasRevenueBasedPricing: true, active: true },
	  {id: 12, name: 'Legacy Small Revenue', price: -1, description: '+Pro Features and', hasRevenueBasedPricing: true, active: true },
      // New AI strategist plans
      {id: 13, name: 'Strategist Lite', price: 20.00, description: 'Basic AI strategist with limited messages', hasRevenueBasedPricing: false, active: true },
      {id: 14, name: 'Strategist', price: -1, description: 'Standard AI strategist with increased message limit', hasRevenueBasedPricing: true, active: true },
      {id: 15, name: 'Strategist Max', price: -1, description: 'Advanced AI strategist with high message limit', hasRevenueBasedPricing: true, active: true },
      {id: 16, name: 'Agency Platform', price: 499.00, description: 'Full-featured AI strategist for agencies', hasRevenueBasedPricing: false, active: true },
      {id: 17, name: 'Extra Agency Brand', price: 150.00, description: 'Additional brand for agency customers', hasRevenueBasedPricing: false, active: true },
    ];

    for (const plan of plansData) {
      let existingPlan;
	  try {
		existingPlan = await this.planRepository.findById(plan.id);
	  } catch (e) {
		console.error(e);
	  }

      if (!existingPlan) {
        await this.planRepository.create(plan);
        console.log(`Created plan: ${plan.name}`);
      } else {
        await this.planRepository.updateById(existingPlan.id, plan);
        console.log(`Updated plan: ${plan.name}`);
      }
    }
  }

  private async populatePlanRevenuePricing(): Promise<void> {
    const revenuePricingData: Partial<PlanRevenuePricing>[] = [
		{ planId: 1, revenueUsdMin: 0, revenueUsdMax: 250000, price: 0 },

		{ planId: 2, revenueUsdMin: 0, revenueUsdMax: 250000, price: 19 },
		{ planId: 2, revenueUsdMin: 250000, revenueUsdMax: 500000, price: 29 },
		{ planId: 2, revenueUsdMin: 500000, revenueUsdMax: 1000000, price: 39 },
		{ planId: 2, revenueUsdMin: 1000000, revenueUsdMax: 2500000, price: 59 },
		{ planId: 2, revenueUsdMin: 2500000, revenueUsdMax: 5000000, price: 99 },
		{ planId: 2, revenueUsdMin: 5000000, revenueUsdMax: 7500000, price: 129 },
		{ planId: 2, revenueUsdMin: 7500000, revenueUsdMax: 10000000, price: 199 },
		{ planId: 2, revenueUsdMin: 10000000, revenueUsdMax: 15000000, price: 299 },

		{ planId: 3, revenueUsdMin: 0, revenueUsdMax: 250000, price: 29 },
		{ planId: 3, revenueUsdMin: 250000, revenueUsdMax: 500000, price: 39 },
		{ planId: 3, revenueUsdMin: 500000, revenueUsdMax: 1000000, price: 99 },
		{ planId: 3, revenueUsdMin: 1000000, revenueUsdMax: 2500000, price: 129 },
		{ planId: 3, revenueUsdMin: 2500000, revenueUsdMax: 5000000, price: 299 },
		{ planId: 3, revenueUsdMin: 5000000, revenueUsdMax: 7500000, price: 399 },
		{ planId: 3, revenueUsdMin: 7500000, revenueUsdMax: 10000000, price: 499 },
		{ planId: 3, revenueUsdMin: 10000000, revenueUsdMax: 15000000, price: 599 },

		{ planId: 4, revenueUsdMin: 0, revenueUsdMax: 250000, price: 39 },
		{ planId: 4, revenueUsdMin: 250000, revenueUsdMax: 500000, price: 59 },
		{ planId: 4, revenueUsdMin: 500000, revenueUsdMax: 1000000, price: 119 },
		{ planId: 4, revenueUsdMin: 1000000, revenueUsdMax: 2500000, price: 169 },
		{ planId: 4, revenueUsdMin: 2500000, revenueUsdMax: 5000000, price: 359 },
		{ planId: 4, revenueUsdMin: 5000000, revenueUsdMax: 7500000, price: 469 },
		{ planId: 4, revenueUsdMin: 7500000, revenueUsdMax: 10000000, price: 619 },
		{ planId: 4, revenueUsdMin: 10000000, revenueUsdMax: 15000000, price: 729 },

		{ planId: 5, revenueUsdMin: 0, revenueUsdMax: 250000, price: 39 },
		{ planId: 5, revenueUsdMin: 250000, revenueUsdMax: 500000, price: 49 },
		{ planId: 5, revenueUsdMin: 500000, revenueUsdMax: 1000000, price: 129 },
		{ planId: 5, revenueUsdMin: 1000000, revenueUsdMax: 2500000, price: 179 },
		{ planId: 5, revenueUsdMin: 2500000, revenueUsdMax: 5000000, price: 349 },
		{ planId: 5, revenueUsdMin: 5000000, revenueUsdMax: 7500000, price: 499 },
		{ planId: 5, revenueUsdMin: 7500000, revenueUsdMax: 10000000, price: 599 },
		{ planId: 5, revenueUsdMin: 10000000, revenueUsdMax: 15000000, price: 699 },

		{ planId: 6, revenueUsdMin: 0, revenueUsdMax: 250000, price: 49 },
		{ planId: 6, revenueUsdMin: 250000, revenueUsdMax: 500000, price: 69 },
		{ planId: 6, revenueUsdMin: 500000, revenueUsdMax: 1000000, price: 149 },
		{ planId: 6, revenueUsdMin: 1000000, revenueUsdMax: 2500000, price: 209 },
		{ planId: 6, revenueUsdMin: 2500000, revenueUsdMax: 5000000, price: 409 },
		{ planId: 6, revenueUsdMin: 5000000, revenueUsdMax: 7500000, price: 559 },
		{ planId: 6, revenueUsdMin: 7500000, revenueUsdMax: 10000000, price: 699 },
		{ planId: 6, revenueUsdMin: 10000000, revenueUsdMax: 15000000, price: 799 },

		{ planId: 8, revenueUsdMin: 0, revenueUsdMax: 250000, price: 0 },

		{ planId: 9, revenueUsdMin: 0, revenueUsdMax: 250000, price: 39 },
		{ planId: 9, revenueUsdMin: 250000, revenueUsdMax: 500000, price: 99 },
		{ planId: 9, revenueUsdMin: 500000, revenueUsdMax: 1000000, price: 149 },
		{ planId: 9, revenueUsdMin: 1000000, revenueUsdMax: 2500000, price: 199 },
		{ planId: 9, revenueUsdMin: 2500000, revenueUsdMax: 5000000, price: 249 },
		{ planId: 9, revenueUsdMin: 5000000, revenueUsdMax: 7500000, price: 299 },
		{ planId: 9, revenueUsdMin: 7500000, revenueUsdMax: 10000000, price: 349 },
		{ planId: 9, revenueUsdMin: 10000000, revenueUsdMax: 15000000, price: 399 },

		{ planId: 10, revenueUsdMin: 0, revenueUsdMax: 250000, price: 129 },
		{ planId: 10, revenueUsdMin: 250000, revenueUsdMax: 500000, price: 149 },
		{ planId: 10, revenueUsdMin: 500000, revenueUsdMax: 1000000, price: 199 },
		{ planId: 10, revenueUsdMin: 1000000, revenueUsdMax: 2500000, price: 299 },
		{ planId: 10, revenueUsdMin: 2500000, revenueUsdMax: 5000000, price: 349 },
		{ planId: 10, revenueUsdMin: 5000000, revenueUsdMax: 7500000, price: 399 },
		{ planId: 10, revenueUsdMin: 7500000, revenueUsdMax: 10000000, price: 499 },
		{ planId: 10, revenueUsdMin: 10000000, revenueUsdMax: 15000000, price: 599 },


		{ planId: 11, revenueUsdMin: 0, revenueUsdMax: 250000, price: 399 },
		{ planId: 11, revenueUsdMin: 250000, revenueUsdMax: 500000, price: 499 },
		{ planId: 11, revenueUsdMin: 500000, revenueUsdMax: 1000000, price: 599 },
		{ planId: 11, revenueUsdMin: 1000000, revenueUsdMax: 2500000, price: 699 },
		{ planId: 11, revenueUsdMin: 2500000, revenueUsdMax: 5000000, price: 799 },
		{ planId: 11, revenueUsdMin: 5000000, revenueUsdMax: 7500000, price: 899 },
		{ planId: 11, revenueUsdMin: 7500000, revenueUsdMax: 10000000, price: 999 },
		{ planId: 11, revenueUsdMin: 10000000, revenueUsdMax: 15000000, price: 1199 },

		{ planId: 12, revenueUsdMin: 0, revenueUsdMax: 250000, price: 0 },

		// New AI strategist plans - fixed pricing, not revenue-based
		{ planId: 13, revenueUsdMin: 0, revenueUsdMax: 100000000, price: 20 },
		{ planId: 14, revenueUsdMin: 0, revenueUsdMax: 100000000, price: 99 },
		{ planId: 15, revenueUsdMin: 0, revenueUsdMax: 100000000, price: 199 },
		{ planId: 16, revenueUsdMin: 0, revenueUsdMax: 100000000, price: 499 },
		{ planId: 17, revenueUsdMin: 0, revenueUsdMax: 100000000, price: 125 },

		// Strategist revenue-based pricing
		{ planId: 14, revenueUsdMin: 0, revenueUsdMax: 250000, price: 99 },
		{ planId: 14, revenueUsdMin: 250000, revenueUsdMax: 500000, price: 99 },
		{ planId: 14, revenueUsdMin: 500000, revenueUsdMax: 1000000, price: 119 },
		{ planId: 14, revenueUsdMin: 1000000, revenueUsdMax: 5000000, price: 199 },
		{ planId: 14, revenueUsdMin: 5000000, revenueUsdMax: 7500000, price: 299 },
		{ planId: 14, revenueUsdMin: 7500000, revenueUsdMax: 10000000, price: 349 },
		{ planId: 14, revenueUsdMin: 10000000, revenueUsdMax: 15000000, price: 499 },

		// Strategist Max revenue-based pricing
		{ planId: 15, revenueUsdMin: 0, revenueUsdMax: 250000, price: 199 },
		{ planId: 15, revenueUsdMin: 250000, revenueUsdMax: 500000, price: 199 },
		{ planId: 15, revenueUsdMin: 500000, revenueUsdMax: 1000000, price: 299 },
		{ planId: 15, revenueUsdMin: 1000000, revenueUsdMax: 5000000, price: 399 },
		{ planId: 15, revenueUsdMin: 5000000, revenueUsdMax: 7500000, price: 699 },
		{ planId: 15, revenueUsdMin: 7500000, revenueUsdMax: 10000000, price: 799 },
		{ planId: 15, revenueUsdMin: 10000000, revenueUsdMax: 15000000, price: 899 },
    ];

    for (const pricing of revenuePricingData) {
      let existingPricing;
      try {
        existingPricing = await this.planRevenuePricingRepository.findOne({
          where: { planId: pricing.planId, revenueUsdMin: pricing.revenueUsdMin },
        });
      } catch (e) {
        console.error(e);
      }

      if (!existingPricing) {
        await this.planRevenuePricingRepository.create(pricing);
        console.log(
          `Created revenue pricing for plan ID: ${pricing.planId} and revenue range: ${pricing.revenueUsdMin} - ${pricing.revenueUsdMax}`
        );
      } else {
        await this.planRevenuePricingRepository.updateById(existingPricing.id, pricing);
        console.log(
          `Updated revenue pricing for plan ID: ${pricing.planId} and revenue range: ${pricing.revenueUsdMin} - ${pricing.revenueUsdMax}`
        );
      }
    }
  }


  /**
   * Populate the Feature model with predefined features.
   */
  private async populateFeatures(): Promise<void> {
    const featuresData: Partial<Feature>[] = [
     	{ id: 'vip', name: 'VIP', description: 'VIP Tiers for loyalty members'},
		{ id: 'points', name: 'Points', description: 'Points earned by members for their activities.' },
		{ id: 'wte-social-follows', name: 'Social Follows', description: 'Enable social media follow options for members.' },
		{ id: 'wte-welcome-bonus', name: 'Welcome Bonus', description: 'Bonus points given to members upon joining.' },
		{ id: 'wte-birthday-bonus', name: 'wte-Birthday-bonus', description: 'wte-Birthday-bonus rewards for members.' },
		{ id: 'reward-free-products', name: 'Points for Free Products', description: 'Earn points towards free products.' },
		{ id: 'referrals', name: 'Referrals', description: 'Reward members for referring others.' },
		{ id: 'campaigns', name: 'Campaigns', description: 'Create and manage loyalty campaigns.' },
		{ id: 'loyalty-app', name: 'Loyalty App', description: 'Provide a dedicated mobile app for loyalty members.' },
		{ id: 'loyalty-notifications', name: 'Loyalty Notifications', description: 'Send notifications to keep members engaged.' },
		{ id: 'product-page-embed', name: 'Product Page Embed', description: 'Embed loyalty features directly on product pages.' },
		{ id: 'thank-you-page-embed', name: 'Thank You P4 	age Embed', description: 'Embed loyalty features on the thank you page.' },
		{ id: 'email-automation', name: 'Email Automation', description: 'Automate loyalty-related emails.' },
		{ id: 'cart-embed', name: 'Cart Embed', description: 'Integrate loyalty features within the shopping cart.' },
		{ id: 'checkout-extension', name: 'Checkout Extension', description: 'Extend the checkout process with loyalty options.' },
		{ id: 'giveaways', name: 'Giveaways', description: 'Host giveaways for loyalty members.' },
		{ id: 'integrations', name: 'Integrations', description: 'Integrate with third-party services.' },
		{ id: 'performance-dashboard', name: 'Performance Dashboard', description: 'Track loyalty program performance.' },
		{ id: 'member-insights-dashboards', name: 'Member Insights Dashboards', description: 'Gain insights into member behavior.' },
		{ id: 'ai-segments', name: 'AI Segments', description: 'Use AI to segment members for targeted actions.' },
		{ id: 'email-and-chat-support', name: 'Email & Chat Support', description: 'Provide support via email and chat for members.' },
		{ id: '1-1-onboarding', name: '1:1 Onboarding', description: 'Personalized onboarding for new members.' },
		{ id: 'dedicated-slack-channel', name: 'Dedicated Slack Channel', description: 'Provide a dedicated Slack channel for support.' },
		{ id: 'gwp-features', name: 'GWP - Features', description: 'Gift with purchase features for loyalty members.' },
		{ id: 'ai-strategist', name: 'AI Strategist', description: 'AI-powered strategist for LVO.' },

        // New features for AI strategist plans
        { id: 'custom-prompts', name: 'Custom Prompts', description: 'Ability to create custom prompts for the AI strategist.' },
        { id: 'account-invites', name: 'Account Invites', description: 'Ability to invite team members to account.' },
        { id: 'klaviyo-integration', name: 'Klaviyo Integration', description: 'Integration with Klaviyo email marketing platform.' },
        { id: 'shopify-integration', name: 'Shopify Integration', description: 'Integration with Shopify e-commerce platform.' },
        { id: 'brands-included', name: 'Brands Included', description: 'Number of brands that can be managed under this plan.' },
    ];

    for (const feature of featuresData) {
      const existingFeature = await this.featureRepository.findOne({
        where: {name: feature.name},
      });
      if (!existingFeature) {
        await this.featureRepository.create(feature);
        console.log(`Created feature: ${feature.name}`);
      } else {
        await this.featureRepository.updateById(existingFeature.id, feature);
        console.log(`Updated feature: ${feature.name}`);
      }
    }
  }


  /**
   * Populate the PlanFeature junction model to establish relationships between Plans and Features.
   */
  private async populatePlanFeatures(): Promise<void> {
    // Define which features belong to which plans
    const planFeaturesData: { planName: string; planFeatures: Partial<PlanFeature>[] }[] = [
          // Start of Selection
          {planName: 'Free', planFeatures: [
    		{ featureId: 'vip', enabled: true, unlimited: true, showInUI: true },
    		{ featureId: 'points', enabled: true, unlimited: true, showInUI: true },
    		{ featureId: 'wte-social-follows', enabled: true, unlimited: true, showInUI: true },
    		{ featureId: 'wte-welcome-bonus', enabled: true, unlimited: true, showInUI: true },
    		{ featureId: 'wte-birthday-bonus', enabled: true, unlimited: true, showInUI: true },
    		{ featureId: 'reward-free-product', enabled: false, showInUI: true },
    		{ featureId: 'referrals', enabled: true, unlimited: true, showInUI: true },
    		{ featureId: 'loyalty-app', enabled: true, unlimited: true, showInUI: true },
    		{ featureId: 'loyalty-notifications', enabled: true, unlimited: true, showInUI: true },
    		{ featureId: 'thank-you-page-embed', enabled: true, unlimited: true, showInUI: true },
    		{ featureId: 'cart-embed', enabled: true, unlimited: true, showInUI: true },
    		{ featureId: 'integrations', enabled: true, limit: 1, showInUI: true },
    		{ featureId: 'performance-dashboard', enabled: true, unlimited: true, showInUI: true },
    		{ featureId: 'email-and-chat-support', enabled: true, unlimited: true, showInUI: true },
    		{ featureId: 'campaigns', enabled: false, showInUI: true },
    		{ featureId: 'product-page-embed', enabled: false, showInUI: true },
    		{ featureId: 'email-automation', enabled: false, showInUI: true },
    		{ featureId: 'checkout-extension', enabled: false, showInUI: true },
    		{ featureId: 'giveaways', enabled: false, showInUI: true },
    		{ featureId: 'member-insights-dashboards', enabled: false, showInUI: true },
    		{ featureId: 'ai-segments', enabled: true, showInUI: true },
    		{ featureId: '1-1-onboarding', enabled: false, showInUI: true },
    		{ featureId: 'dedicated-slack-channel', enabled: false, showInUI: true },
    		{ featureId: 'gwp-features', enabled: false, showInUI: true },
			{ featureId: 'ai-strategist', enabled: false, showInUI: true, limit: 5 },
            // Add new features to existing plan
            { featureId: 'custom-prompts', enabled: true, showInUI: false },
            { featureId: 'account-invites', enabled: true, showInUI: false },
            { featureId: 'klaviyo-integration', enabled: true, showInUI: false },
            { featureId: 'shopify-integration', enabled: true, showInUI: false },
            { featureId: 'brands-included', enabled: false, limit: 0, showInUI: true },
          ]},
		  {planName: 'Growth', planFeatures: [
    		{ featureId: 'vip', enabled: true, unlimited: true, showInUI: true },
    		{ featureId: 'points', enabled: true, unlimited: true, showInUI: true },
    		{ featureId: 'wte-social-follows', enabled: true, unlimited: true, showInUI: true },
    		{ featureId: 'wte-welcome-bonus', enabled: true, unlimited: true, showInUI: true },
    		{ featureId: 'wte-birthday-bonus', enabled: true, unlimited: true, showInUI: true },
    		{ featureId: 'reward-free-product', enabled: true, showInUI: true },
    		{ featureId: 'referrals', enabled: true, unlimited: true, showInUI: true },
    		{ featureId: 'loyalty-app', enabled: true, unlimited: true, showInUI: true },
    		{ featureId: 'loyalty-notifications', enabled: true, unlimited: true, showInUI: true },
    		{ featureId: 'thank-you-page-embed', enabled: true, unlimited: true, showInUI: true },
    		{ featureId: 'cart-embed', enabled: true, unlimited: true, showInUI: true },
    		{ featureId: 'integrations', enabled: true, limit: 1, showInUI: true },
    		{ featureId: 'performance-dashboard', enabled: true, unlimited: true, showInUI: true },
    		{ featureId: 'email-and-chat-support', enabled: true, unlimited: true, showInUI: true },
    		{ featureId: 'campaigns', enabled: false, showInUI: true },
    		{ featureId: 'product-page-embed', enabled: true, showInUI: true },
    		{ featureId: 'email-automation', enabled: true, showInUI: true },
    		{ featureId: 'checkout-extension', enabled: false, showInUI: true },
    		{ featureId: 'giveaways', enabled: true, showInUI: true },
    		{ featureId: 'member-insights-dashboards', enabled: true, showInUI: true },
    		{ featureId: 'ai-segments', enabled: true, showInUI: true },
    		{ featureId: '1-1-onboarding', enabled: false, showInUI: true },
    		{ featureId: 'dedicated-slack-channel', enabled: false, showInUI: true },
    		{ featureId: 'gwp-features', enabled: true, showInUI: true },
			{ featureId: 'ai-strategist', enabled: true, showInUI: true, limit: 30 },
            // Add new features to existing plan
            { featureId: 'custom-prompts', enabled: true, showInUI: false },
            { featureId: 'account-invites', enabled: true, limit: 2, showInUI: true },
            { featureId: 'klaviyo-integration', enabled: true, showInUI: false },
            { featureId: 'shopify-integration', enabled: true, showInUI: false },
            { featureId: 'brands-included', enabled: false, limit: 0, showInUI: true },
          ]},
		  {planName: 'Pro', planFeatures: [
    		{ featureId: 'vip', enabled: true, unlimited: true, showInUI: true },
    		{ featureId: 'points', enabled: true, unlimited: true, showInUI: true },
    		{ featureId: 'wte-social-follows', enabled: true, unlimited: true, showInUI: true },
    		{ featureId: 'wte-welcome-bonus', enabled: true, unlimited: true, showInUI: true },
    		{ featureId: 'wte-birthday-bonus', enabled: true, unlimited: true, showInUI: true },
    		{ featureId: 'reward-free-product', enabled: true, showInUI: true },
    		{ featureId: 'referrals', enabled: true, unlimited: true, showInUI: true },
    		{ featureId: 'loyalty-app', enabled: true, unlimited: true, showInUI: true },
    		{ featureId: 'loyalty-notifications', enabled: true, unlimited: true, showInUI: true },
    		{ featureId: 'thank-you-page-embed', enabled: true, unlimited: true, showInUI: true },
    		{ featureId: 'cart-embed', enabled: true, unlimited: true, showInUI: true },
    		{ featureId: 'integrations', enabled: true, limit: 1, showInUI: true },
    		{ featureId: 'performance-dashboard', enabled: true, unlimited: true, showInUI: true },
    		{ featureId: 'email-and-chat-support', enabled: true, unlimited: true, showInUI: true },
    		{ featureId: 'campaigns', enabled: true, showInUI: true },
    		{ featureId: 'product-page-embed', enabled: true, showInUI: true },
    		{ featureId: 'email-automation', enabled: true, showInUI: true },
    		{ featureId: 'checkout-extension', enabled: true, showInUI: true },
    		{ featureId: 'giveaways', enabled: true, showInUI: true },
    		{ featureId: 'member-insights-dashboards', enabled: true, showInUI: true },
    		{ featureId: 'ai-segments', enabled: true, showInUI: true },
    		{ featureId: '1-1-onboarding', enabled: true, showInUI: true },
    		{ featureId: 'dedicated-slack-channel', enabled: true, showInUI: true },
    		{ featureId: 'gwp-features', enabled: true, showInUI: true },
			{ featureId: 'ai-strategist', enabled: true, showInUI: true, limit: 30 },
            // Add new features to existing plan
            { featureId: 'custom-prompts', enabled: true, showInUI: true },
            { featureId: 'account-invites', enabled: true, limit: 5, showInUI: true },
            { featureId: 'klaviyo-integration', enabled: true, showInUI: true },
            { featureId: 'shopify-integration', enabled: true, showInUI: true },
            { featureId: 'brands-included', enabled: false, limit: 0, showInUI: true },
          ]},
          {planName: 'Enterprise', planFeatures: [
    		{ featureId: 'vip', enabled: true, unlimited: true, showInUI: true },
    		{ featureId: 'points', enabled: true, unlimited: true, showInUI: true },
    		{ featureId: 'wte-social-follows', enabled: true, unlimited: true, showInUI: true },
    		{ featureId: 'wte-welcome-bonus', enabled: true, unlimited: true, showInUI: true },
    		{ featureId: 'wte-birthday-bonus', enabled: true, unlimited: true, showInUI: true },
    		{ featureId: 'reward-free-product', enabled: true, unlimited: true, showInUI: true },
    		{ featureId: 'referrals', enabled: true, unlimited: true, showInUI: true },
    		{ featureId: 'loyalty-app', enabled: true, unlimited: true, showInUI: true },
    		{ featureId: 'loyalty-notifications', enabled: true, unlimited: true, showInUI: true },
    		{ featureId: 'thank-you-page-embed', enabled: true, unlimited: true, showInUI: true },
    		{ featureId: 'cart-embed', enabled: true, unlimited: true, showInUI: true },
    		{ featureId: 'integrations', enabled: true, unlimited: true, showInUI: true },
    		{ featureId: 'performance-dashboard', enabled: true, unlimited: true, showInUI: true },
    		{ featureId: 'email-and-chat-support', enabled: true, unlimited: true, showInUI: true },
    		{ featureId: 'campaigns', enabled: true, unlimited: true, showInUI: true  },
    		{ featureId: 'product-page-embed', enabled: true, unlimited: true, showInUI: true  },
    		{ featureId: 'email-automation', enabled: true, unlimited: true, showInUI: true  },
    		{ featureId: 'checkout-extension', enabled: true, unlimited: true, showInUI: true  },
    		{ featureId: 'giveaways', enabled: true, unlimited: true, showInUI: true  },
    		{ featureId: 'member-insights-dashboards', enabled: true, unlimited: true, showInUI: true  },
    		{ featureId: 'ai-segments', enabled: true, unlimited: true, showInUI: true  },
    		{ featureId: '1-1-onboarding', enabled: true, unlimited: true, showInUI: true  },
    		{ featureId: 'dedicated-slack-channel', enabled: true, unlimited: true, showInUI: true  },
    		{ featureId: 'gwp-features', enabled: true, unlimited: true, showInUI: true },
			{ featureId: 'ai-strategist', enabled: true, unlimited: true, showInUI: true, limit: 30 },
            // Add new features to existing plan
            { featureId: 'custom-prompts', enabled: true, unlimited: true, showInUI: true },
            { featureId: 'account-invites', enabled: true, unlimited: true, showInUI: true },
            { featureId: 'klaviyo-integration', enabled: true, showInUI: true },
            { featureId: 'shopify-integration', enabled: true, showInUI: true },
            { featureId: 'brands-included', enabled: true, unlimited: true, showInUI: true },
          ]},
          {planName: 'Legacy Small Revenue', planFeatures: [
    		{ featureId: 'vip', enabled: true, unlimited: true, showInUI: true },
    		{ featureId: 'points', enabled: true, unlimited: true, showInUI: true },
    		{ featureId: 'wte-social-follows', enabled: true, unlimited: true, showInUI: true },
    		{ featureId: 'wte-welcome-bonus', enabled: true, unlimited: true, showInUI: true },
    		{ featureId: 'wte-birthday-bonus', enabled: true, unlimited: true, showInUI: true },
    		{ featureId: 'reward-free-product', enabled: true, unlimited: true, showInUI: true },
    		{ featureId: 'referrals', enabled: true, unlimited: true, showInUI: true },
    		{ featureId: 'loyalty-app', enabled: true, unlimited: true, showInUI: true },
    		{ featureId: 'loyalty-notifications', enabled: true, unlimited: true, showInUI: true },
    		{ featureId: 'thank-you-page-embed', enabled: true, unlimited: true, showInUI: true },
    		{ featureId: 'cart-embed', enabled: true, unlimited: true, showInUI: true },
    		{ featureId: 'integrations', enabled: true, unlimited: true, showInUI: true },
    		{ featureId: 'performance-dashboard', enabled: true, unlimited: true, showInUI: true },
    		{ featureId: 'email-and-chat-support', enabled: true, unlimited: true, showInUI: true },
    		{ featureId: 'campaigns', enabled: true, unlimited: true, showInUI: true  },
    		{ featureId: 'product-page-embed', enabled: true, unlimited: true, showInUI: true  },
    		{ featureId: 'email-automation', enabled: true, unlimited: true, showInUI: true  },
    		{ featureId: 'checkout-extension', enabled: true, unlimited: true, showInUI: true  },
    		{ featureId: 'giveaways', enabled: true, unlimited: true, showInUI: true  },
    		{ featureId: 'member-insights-dashboards', enabled: true, unlimited: true, showInUI: true  },
    		{ featureId: 'ai-segments', enabled: true, unlimited: true, showInUI: true  },
    		{ featureId: '1-1-onboarding', enabled: true, unlimited: true, showInUI: true  },
    		{ featureId: 'dedicated-slack-channel', enabled: true, unlimited: true, showInUI: true  },
    		{ featureId: 'gwp-features', enabled: true, unlimited: true, showInUI: true },
			{ featureId: 'ai-strategist', enabled: true, unlimited: true, showInUI: true, limit: 30 },
            // Add new features to existing plan
            { featureId: 'custom-prompts', enabled: true, unlimited: true, showInUI: true },
            { featureId: 'account-invites', enabled: true, unlimited: true, showInUI: true },
            { featureId: 'klaviyo-integration', enabled: true, showInUI: true },
            { featureId: 'shopify-integration', enabled: true, showInUI: true },
            { featureId: 'brands-included', enabled: false, limit: 0, showInUI: true },
          ]},

          // Add new plans with complete feature sets
          // Strategist Lite - $20/mo
          {planName: 'Strategist Lite', planFeatures: [
            // AI Strategist specific features
            { featureId: 'ai-strategist', enabled: true, limit: 20, showInUI: true },
            { featureId: 'custom-prompts', enabled: false, showInUI: true },
            { featureId: 'account-invites', enabled: false, showInUI: true },
            { featureId: 'integrations', enabled: false, showInUI: true },
            { featureId: 'klaviyo-integration', enabled: false, showInUI: true },
            { featureId: 'shopify-integration', enabled: false, showInUI: true },
            { featureId: 'ai-segments', enabled: false, showInUI: true },
            { featureId: 'email-and-chat-support', enabled: true, showInUI: true },
            { featureId: 'performance-dashboard', enabled: true, showInUI: true },

            // Standard features (disabled)
            { featureId: 'vip', enabled: false, showInUI: false },
            { featureId: 'points', enabled: false, showInUI: false },
            { featureId: 'wte-social-follows', enabled: false, showInUI: false },
            { featureId: 'wte-welcome-bonus', enabled: false, showInUI: false },
            { featureId: 'wte-birthday-bonus', enabled: false, showInUI: false },
            { featureId: 'reward-free-product', enabled: false, showInUI: false },
            { featureId: 'reward-free-products', enabled: false, showInUI: false },
            { featureId: 'referrals', enabled: false, showInUI: false },
            { featureId: 'campaigns', enabled: false, showInUI: false },
            { featureId: 'loyalty-app', enabled: false, showInUI: false },
            { featureId: 'loyalty-notifications', enabled: false, showInUI: false },
            { featureId: 'product-page-embed', enabled: false, showInUI: false },
            { featureId: 'thank-you-page-embed', enabled: false, showInUI: false },
            { featureId: 'email-automation', enabled: false, showInUI: false },
            { featureId: 'cart-embed', enabled: false, showInUI: false },
            { featureId: 'checkout-extension', enabled: false, showInUI: false },
            { featureId: 'giveaways', enabled: false, showInUI: false },
            { featureId: 'member-insights-dashboards', enabled: false, showInUI: false },
            { featureId: '1-1-onboarding', enabled: false, showInUI: false },
            { featureId: 'dedicated-slack-channel', enabled: false, showInUI: false },
            { featureId: 'gwp-features', enabled: false, showInUI: false },
            { featureId: 'brands-included', enabled: false, limit: 0, showInUI: true },
          ]},

          // Strategist - $99/mo
          {planName: 'Strategist', planFeatures: [
            // AI Strategist specific features
            { featureId: 'ai-strategist', enabled: true, limit: 30, showInUI: true },
            { featureId: 'custom-prompts', enabled: false, showInUI: true },
            { featureId: 'account-invites', enabled: true, limit: 3, showInUI: true },
            { featureId: 'integrations', enabled: true, limit: 2, showInUI: true },
            { featureId: 'klaviyo-integration', enabled: true, showInUI: true },
            { featureId: 'shopify-integration', enabled: true, showInUI: true },
            { featureId: 'ai-segments', enabled: false, showInUI: true },
            { featureId: 'email-and-chat-support', enabled: true, showInUI: true },
            { featureId: '1-1-onboarding', enabled: true, showInUI: true },
            { featureId: 'performance-dashboard', enabled: true, showInUI: true },

            // Standard features (disabled)
            { featureId: 'vip', enabled: false, showInUI: false },
            { featureId: 'points', enabled: false, showInUI: false },
            { featureId: 'wte-social-follows', enabled: false, showInUI: false },
            { featureId: 'wte-welcome-bonus', enabled: false, showInUI: false },
            { featureId: 'wte-birthday-bonus', enabled: false, showInUI: false },
            { featureId: 'reward-free-product', enabled: false, showInUI: false },
            { featureId: 'reward-free-products', enabled: false, showInUI: false },
            { featureId: 'referrals', enabled: false, showInUI: false },
            { featureId: 'campaigns', enabled: false, showInUI: false },
            { featureId: 'loyalty-app', enabled: false, showInUI: false },
            { featureId: 'loyalty-notifications', enabled: false, showInUI: false },
            { featureId: 'product-page-embed', enabled: false, showInUI: false },
            { featureId: 'thank-you-page-embed', enabled: false, showInUI: false },
            { featureId: 'email-automation', enabled: false, showInUI: false },
            { featureId: 'cart-embed', enabled: false, showInUI: false },
            { featureId: 'checkout-extension', enabled: false, showInUI: false },
            { featureId: 'giveaways', enabled: false, showInUI: false },
            { featureId: 'member-insights-dashboards', enabled: false, showInUI: false },
            { featureId: 'dedicated-slack-channel', enabled: false, showInUI: false },
            { featureId: 'gwp-features', enabled: false, showInUI: false },
            { featureId: 'brands-included', enabled: false, limit: 0, showInUI: true },
          ]},

          // Strategist Max - $199/mo
          {planName: 'Strategist Max', planFeatures: [
            // AI Strategist specific features
            { featureId: 'ai-strategist', enabled: true, limit: 50, showInUI: true },
            { featureId: 'custom-prompts', enabled: true, showInUI: true },
            { featureId: 'account-invites', enabled: true, limit: 5, showInUI: true },
            { featureId: 'integrations', enabled: true, unlimited: true, showInUI: true },
            { featureId: 'klaviyo-integration', enabled: true, showInUI: true },
            { featureId: 'shopify-integration', enabled: true, showInUI: true },
            { featureId: 'ai-segments', enabled: true, showInUI: true },
            { featureId: 'email-and-chat-support', enabled: true, showInUI: true },
            { featureId: '1-1-onboarding', enabled: true, showInUI: true },
            { featureId: 'dedicated-slack-channel', enabled: true, showInUI: true },
            { featureId: 'performance-dashboard', enabled: true, showInUI: true },
            { featureId: 'member-insights-dashboards', enabled: true, showInUI: true },

            // Standard features (disabled)
            { featureId: 'vip', enabled: false, showInUI: false },
            { featureId: 'points', enabled: false, showInUI: false },
            { featureId: 'wte-social-follows', enabled: false, showInUI: false },
            { featureId: 'wte-welcome-bonus', enabled: false, showInUI: false },
            { featureId: 'wte-birthday-bonus', enabled: false, showInUI: false },
            { featureId: 'reward-free-product', enabled: false, showInUI: false },
            { featureId: 'reward-free-products', enabled: false, showInUI: false },
            { featureId: 'referrals', enabled: false, showInUI: false },
            { featureId: 'campaigns', enabled: false, showInUI: false },
            { featureId: 'loyalty-app', enabled: false, showInUI: false },
            { featureId: 'loyalty-notifications', enabled: false, showInUI: false },
            { featureId: 'product-page-embed', enabled: false, showInUI: false },
            { featureId: 'thank-you-page-embed', enabled: false, showInUI: false },
            { featureId: 'email-automation', enabled: false, showInUI: false },
            { featureId: 'cart-embed', enabled: false, showInUI: false },
            { featureId: 'checkout-extension', enabled: false, showInUI: false },
            { featureId: 'giveaways', enabled: false, showInUI: false },
            { featureId: 'gwp-features', enabled: false, showInUI: false },
            { featureId: 'brands-included', enabled: false, limit: 0, showInUI: true },
          ]},

          // Agency Platform - $499/mo
          {planName: 'Agency Platform', planFeatures: [
            // AI Strategist specific features
            { featureId: 'ai-strategist', enabled: true, unlimited: true, showInUI: true, limit: 100 },
            { featureId: 'custom-prompts', enabled: true, showInUI: true },
            { featureId: 'account-invites', enabled: true, limit: 10, showInUI: true },
            { featureId: 'integrations', enabled: true, limit: 2, showInUI: true },
            { featureId: 'klaviyo-integration', enabled: true, showInUI: true },
            { featureId: 'shopify-integration', enabled: true, showInUI: true },
            { featureId: 'ai-segments', enabled: true, showInUI: true },
            { featureId: 'email-and-chat-support', enabled: true, showInUI: true },
            { featureId: '1-1-onboarding', enabled: true, showInUI: true },
            { featureId: 'dedicated-slack-channel', enabled: true, showInUI: true },
            { featureId: 'performance-dashboard', enabled: true, showInUI: true },
            { featureId: 'member-insights-dashboards', enabled: true, showInUI: true },

            // Standard features (disabled)
            { featureId: 'vip', enabled: false, showInUI: false },
            { featureId: 'points', enabled: false, showInUI: false },
            { featureId: 'wte-social-follows', enabled: false, showInUI: false },
            { featureId: 'wte-welcome-bonus', enabled: false, showInUI: false },
            { featureId: 'wte-birthday-bonus', enabled: false, showInUI: false },
            { featureId: 'reward-free-product', enabled: false, showInUI: false },
            { featureId: 'reward-free-products', enabled: false, showInUI: false },
            { featureId: 'referrals', enabled: false, showInUI: false },
            { featureId: 'campaigns', enabled: false, showInUI: false },
            { featureId: 'loyalty-app', enabled: false, showInUI: false },
            { featureId: 'loyalty-notifications', enabled: false, showInUI: false },
            { featureId: 'product-page-embed', enabled: false, showInUI: false },
            { featureId: 'thank-you-page-embed', enabled: false, showInUI: false },
            { featureId: 'email-automation', enabled: false, showInUI: false },
            { featureId: 'cart-embed', enabled: false, showInUI: false },
            { featureId: 'checkout-extension', enabled: false, showInUI: false },
            { featureId: 'giveaways', enabled: false, showInUI: false },
            { featureId: 'gwp-features', enabled: false, showInUI: false },
            { featureId: 'brands-included', enabled: true, limit: 2, showInUI: true },
          ]},

          // Extra Agency Brand - $125/mo
          {planName: 'Extra Agency Brand', planFeatures: [
            // AI Strategist specific features
            { featureId: 'ai-strategist', enabled: true, unlimited: true, showInUI: true, limit: 100 },
            { featureId: 'custom-prompts', enabled: true, showInUI: true },
            { featureId: 'account-invites', enabled: true, limit: 3, showInUI: true },
            { featureId: 'integrations', enabled: true, limit: 2, showInUI: true },
            { featureId: 'klaviyo-integration', enabled: true, showInUI: true },
            { featureId: 'shopify-integration', enabled: true, showInUI: true },
            { featureId: 'ai-segments', enabled: true, showInUI: true },
            { featureId: 'email-and-chat-support', enabled: true, showInUI: true },
            { featureId: 'performance-dashboard', enabled: true, showInUI: true },
            { featureId: 'member-insights-dashboards', enabled: true, showInUI: true },

            // Standard features (disabled)
            { featureId: 'vip', enabled: false, showInUI: false },
            { featureId: 'points', enabled: false, showInUI: false },
            { featureId: 'wte-social-follows', enabled: false, showInUI: false },
            { featureId: 'wte-welcome-bonus', enabled: false, showInUI: false },
            { featureId: 'wte-birthday-bonus', enabled: false, showInUI: false },
            { featureId: 'reward-free-product', enabled: false, showInUI: false },
            { featureId: 'reward-free-products', enabled: false, showInUI: false },
            { featureId: 'referrals', enabled: false, showInUI: false },
            { featureId: 'campaigns', enabled: false, showInUI: false },
            { featureId: 'loyalty-app', enabled: false, showInUI: false },
            { featureId: 'loyalty-notifications', enabled: false, showInUI: false },
            { featureId: 'product-page-embed', enabled: false, showInUI: false },
            { featureId: 'thank-you-page-embed', enabled: false, showInUI: false },
            { featureId: 'email-automation', enabled: false, showInUI: false },
            { featureId: 'cart-embed', enabled: false, showInUI: false },
            { featureId: 'checkout-extension', enabled: false, showInUI: false },
            { featureId: 'giveaways', enabled: false, showInUI: false },
            { featureId: '1-1-onboarding', enabled: false, showInUI: true },
            { featureId: 'dedicated-slack-channel', enabled: false, showInUI: false },
            { featureId: 'gwp-features', enabled: false, showInUI: false },
            { featureId: 'brands-included', enabled: false, limit: 0, showInUI: true },
          ]},
    ];

	try {
		verifyPlanFeatures(planFeaturesData);
	} catch (error) {
		console.error(error.message);
	}    // Get all plans and features in one query
    const plans = await this.planRepository.find();
    const plansMap = new Map(plans.map(p => [p.name, p]));

    // Get all existing plan features in one query
    const existingPlanFeatures = await this.planFeatureRepository.find();
    const existingPlanFeaturesMap = new Map();

    // Create a map with composite key of planId-featureId for O(1) lookups
    existingPlanFeatures.forEach(pf => {
      const key = `${pf.planId}-${pf.featureId}`;
      existingPlanFeaturesMap.set(key, pf);
    });

    for (const pf of planFeaturesData) {
      const plan = plansMap.get(pf.planName);

      if (!plan) {
        console.error(`Plan not found: ${pf.planName}`);
        continue;
      }

      for (const planFeature of pf.planFeatures) {
        const key = `${plan.id}-${planFeature.featureId}`;
        const existingPlanFeature = existingPlanFeaturesMap.get(key);

        if (!existingPlanFeature) {
          await this.planFeatureRepository.create({
            planId: plan.id,
            ...planFeature
          });
          console.log(`Associated Feature '${planFeature.featureId}' with Plan '${plan.name}'`);
        } else {
          await this.planFeatureRepository.updateById(existingPlanFeature.id, {
            ...existingPlanFeature,
            ...planFeature
          });
          console.log(`Updating existing association between Feature '${planFeature.featureId}' and Plan '${plan.name}'`);
        }
      }
    }
  }

  /**
   * This method will be invoked when the application stops.
   */
  async stop(): Promise<void> {
    // Implement any cleanup logic if necessary
    console.log('FeatureManagementObserver has stopped.');
  }
}


function verifyPlanFeatures(planFeaturesData: { planName: string; planFeatures: Partial<PlanFeature>[] }[]) {
	if (planFeaturesData.length === 0) {
	  throw new Error('No plan features data provided');
	}

	// Extract the feature IDs from the first plan as the reference set
	const referenceFeatureSet = new Set<any>(planFeaturesData[0].planFeatures.map(feature => feature.featureId));

	for (const plan of planFeaturesData) {
	  const currentFeatureSet = new Set<any>(plan.planFeatures.map(feature => feature.featureId));

	  if (!areSetsEqual(referenceFeatureSet, currentFeatureSet)) {
		throw new Error(`Mismatch in plan features for plan: ${plan.planName}`);
	  }
	}

	console.log('All plans have the same set of features');
  }

  function areSetsEqual(setA: Set<string>, setB: Set<string>): boolean {
	if (setA.size !== setB.size) return false;
	for (const item of setA) {
	  if (!setB.has(item)) return false;
	}
	return true;
  }

