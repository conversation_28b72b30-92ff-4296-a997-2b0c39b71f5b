import {
	repository,
} from '@loopback/repository';
import {
	param,
	get,
	api,
	getModelSchemaRef,
	post,
	requestBody,
	HttpErrors,
	patch,
} from '@loopback/rest';
import {
	OrganizationSegment,
	MetricSegment,
	RaleonUserIdentity,
} from '../models';
import {IntegrationRepository, MetricSegmentRepository, OrganizationIntegrationDetailsRepository, OrganizationKeysRepository, OrganizationSegmentDetailsRepository, OrganizationSegmentRepository, RaleonUserIdentityRepository} from '../repositories';
import {modelForGuard, modelIdForGuard, OrgGuardPropertyStrategy, restrictReadsWithGuard, skipGuardCheck} from '../interceptors/crud-guard.interceptor';
import {injectGuardedFilter, injectUserOrgId, guardStrategy} from '../interceptors/crud-guard.interceptor';
import {authenticate} from '@loopback/authentication';
import {authorize} from '@loopback/authorization';
import {basicAuthorization} from '../services/basic.authorizor';
import {OrganizationSegmentDetails} from '../models/organization-segment-details.model';
import {service} from '@loopback/core';
import {ShopperInfo} from '../services';
import {KlaviyoService} from '../services/integrations/klaviyo.service';
import {KLAVIYO_INTEGRATION_KEY} from './integrations.controller';
import {OrganizationMetricSegmentRepository} from '../repositories/organization-metric-segment.repository';
import {MemcachedLockService} from '../services/mem-cached.service';
const {faker} = require('@faker-js/faker');
const NodeCache = require( "node-cache" );
const segmentCache = new NodeCache({ stdTTL: 900, checkperiod: 120 });


@api({basePath: '/api/v1'})
@guardStrategy(new OrgGuardPropertyStrategy<OrganizationSegment>({
	orgIdModelPropertyName: 'orgId',
	repositoryClass: OrganizationSegmentRepository
}))
export class OrganizationSegmentController {
	constructor(
		@repository(OrganizationSegmentRepository)
		public organizationSegmentRepository: OrganizationSegmentRepository,
		@repository(OrganizationMetricSegmentRepository)
		public organizationMetricSegmentRepository: OrganizationMetricSegmentRepository,
		@repository(OrganizationSegmentDetailsRepository)
		public organizationSegmentDetailsRepository: OrganizationSegmentDetailsRepository,
		@repository(MetricSegmentRepository)
		public metricSegmentRepository: MetricSegmentRepository,
		@repository(RaleonUserIdentityRepository)
		public raleonUserIdentityRepository: RaleonUserIdentityRepository,
		@repository(OrganizationKeysRepository)
		public organizationKeysRepository: OrganizationKeysRepository,
		@service(ShopperInfo)
		public shopperInfo: ShopperInfo,
		@service(KlaviyoService)
		public klaviyoService: KlaviyoService,
		@repository(OrganizationKeysRepository)
		public orgKeysRepository: OrganizationKeysRepository,
		@repository(OrganizationIntegrationDetailsRepository)
		public organizationIntegrationDetailsRepository: OrganizationIntegrationDetailsRepository,
	) { }

	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@skipGuardCheck()
	@get('/organization-segment/check-klaviyo-sync', {
		responses: {
			'200': {
				description: 'Check if Klaviyo profiles have been synced with Raleon Identity Value',
				content: {
					'application/json': {
						schema: {
							type: 'object',
							properties: {
								synced: { type: 'boolean' },
							},
						},
					},
				},
			},
		},
	})
	async checkKlaviyoProfileSync(
		@injectUserOrgId() orgId: number,
	): Promise<{synced: boolean}> {
		return this.klaviyoService.checkProfileSync(orgId);
	}

	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@skipGuardCheck()
	@get('/organization-segment/klaviyo-segments', {
		responses: {
			'200': {
				description: 'Get all Klaviyo segments for the organization',
				content: {
					'application/json': {
						schema: {
							type: 'array',
							items: {
								type: 'object',
								properties: {
									id: { type: 'string' },
									name: { type: 'string' },
									description: { type: 'string' }
								}
							}
						},
					},
				},
			},
		},
	})
	async getKlaviyoSegments(
		@injectUserOrgId() orgId: number,
	): Promise<any[]> {
		try {
			const segments = await this.klaviyoService.getSegments(orgId);

			// Transform the segments to a simpler format for the frontend
			return segments.map(segment => ({
				id: segment.id,
				name: segment.attributes.name,
				description: segment.attributes.definition?.condition_groups?.length > 0
					? `${segment.attributes.definition.condition_groups.length} condition group(s)`
					: 'No conditions defined'
			}));
		} catch (error) {
			console.error('Error fetching Klaviyo segments:', error);
			throw new HttpErrors.InternalServerError('Failed to fetch Klaviyo segments');
		}
	}

	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@skipGuardCheck()
	@get('/organization-segment/klaviyo-sync-progress', {
		responses: {
			'200': {
				description: 'Get progress of Klaviyo segment synchronization',
				content: {
					'application/json': {
						schema: {
							type: 'object',
							properties: {
								total: { type: 'number' },
								synced: { type: 'number' },
								progress: { type: 'number' }
							},
						},
					},
				},
			},
		},
	})
	async getKlaviyoSyncProgress(
		@injectUserOrgId() orgId: number,
	): Promise<{total: number; synced: number; progress: number}> {
		console.log("Klaviyo sync progress check for orgId:", orgId);
		try {
			// Check if Klaviyo integration is enabled
			const klaviyoKey = await this.orgKeysRepository.findOne({
				where: {organizationId: orgId, key: KLAVIYO_INTEGRATION_KEY},
			});

			if (!klaviyoKey) {
				return {
					total: 0,
					synced: 0,
					progress: 100
				};
			}

			const details = await this.organizationIntegrationDetailsRepository.findOne({
				where: {
					orgId,
					integrationId: 2
				}
			});
			if (details && details.connectedDate === 'scope-failure') {
				return {
					total: 0,
					synced: 0,
					progress: -1
				};
			}

			// Get all metric segments with Klaviyo integration ID
			const metricSegments = await this.metricSegmentRepository.find({
				where: {
					integrationId: 2 // Klaviyo integration ID
				}
			});

			// Filter out segments without valid queries
			const validMetricSegments = metricSegments.filter(segment => {
				try {
					if (!segment.query) return false;
					const parsedQuery = JSON.parse(segment.query);
					return parsedQuery.conditions?.[0] !== undefined;
				} catch (error) {
					console.error(`Error parsing query for segment ${segment.name}:`, error);
					return false;
				}
			});

			const total = validMetricSegments.length;

			// Get all Klaviyo segments in one request
			const klaviyoSegments = await this.klaviyoService.getSegments(orgId);

			// Get all organization segments that have externalIds
			const orgSegments = await this.organizationSegmentRepository.find({
				where: {
					orgId,
					externalId: {neq: (null as any)}
				}
			});

			// Check each organization segment against Klaviyo segments
			for (const orgSegment of orgSegments) {
				const exists = klaviyoSegments.some(ks => ks.id === orgSegment.externalId);
				if (!exists) {
					// If the segment doesn't exist in Klaviyo anymore, clear the external references
					await this.organizationSegmentRepository.updateById(orgSegment.id, {
						externalId: null as any,
						externalSyncDate: null as any
					});
				}
			}

			// Count how many segments exist in Klaviyo
			const synced = validMetricSegments.reduce((count, segment) => {
				const metricName = `Raleon Stats - ${segment.name}`;
				return klaviyoSegments.some(ks => ks.attributes.name === metricName) ? count + 1 : count;
			}, 0);

			const progress = total > 0 ? Math.round((synced / total) * 100) : 100;
			console.log("END Klaviyo sync progress check for orgId:", orgId);
			return {
				total,
				synced,
				progress
			};
		} catch (error) {
			console.error('Error getting Klaviyo sync progress:', error);
			throw new HttpErrors.InternalServerError('Failed to get Klaviyo sync progress');
		}
	}

	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@skipGuardCheck()
	@post('/organization-segment/{id}/integration-sync', {
		responses: {
			'200': {
				description: 'Sync Organization Segment with external integration',
				content: {
					'application/json': {
						schema: {
							type: 'object',
							properties: {
								success: {type: 'boolean'},
								externalId: {type: 'string'},
							},
						},
					},
				},
			},
		},
	})
	async syncSegmentIntegration(
		@param.path.number('id') id: number,
		@injectUserOrgId() orgId: number,
	): Promise<{success: boolean, externalId?: string}> {
		try {
			const segment = await this.organizationSegmentRepository.findById(id);

			if (segment.orgId !== orgId) {
				throw new HttpErrors.Forbidden('You are not authorized to access this segment');
			}

			const segmentDetails = await this.organizationSegmentDetailsRepository.find({
				where: {orgSegmentId: segment.id},
				include: [{relation: 'metricSegment'}],
			});

			const data = {
				name: segment.name,
				description: segment.description,
				positive: segmentDetails
				  .filter(detail => detail.include)
				  .map(detail => ({
					...detail.metricSegment,
					query: detail.queryOverride || detail.metricSegment?.query
				  })),
				negative: segmentDetails
				  .filter(detail => !detail.include)
				  .map(detail => ({
					...detail.metricSegment,
					query: detail.queryOverride || detail.metricSegment?.query
				  }))
			  };
			let externalId = segment.externalId;
			try {
				if (externalId) {
					externalId = await this.klaviyoService.updateSegment(orgId, externalId, data);
				} else {
					externalId = await this.klaviyoService.createSegment(orgId, data);
				}
				segment.externalId = externalId;
				segment.externalSyncDate = new Date();
			} catch (error) {
				console.error('Error syncing segment with Klaviyo:', error);
				if (error instanceof HttpErrors.HttpError) {
					throw error; // Pass through HTTP errors from Klaviyo service
				}
				throw new HttpErrors.InternalServerError('Failed to sync segment with integration: ' + error.message);
			}

			await this.organizationSegmentRepository.updateById(segment.id, segment);

			return {success: true, externalId};
		} catch (error) {
			console.error('Error syncing segment with Klaviyo:', error);
			if (error instanceof HttpErrors.HttpError) {
				throw error; // Pass through HTTP errors from Klaviyo service
			}
			throw new HttpErrors.InternalServerError('Failed to sync segment with integration: ' + error.message);
		}
	}

	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@skipGuardCheck()
	@get('/organization-segment', {
		responses: {
			'200': {
				description: 'Array of OrganizationSegments associated with the Organization',
				content: {
					'application/json': {
						schema: {},
					},
				},
			},
		},
	})
	async getSegment(
		@injectUserOrgId() orgId: number,
	): Promise<any> {
		console.log("Injected orgId:", orgId);
		const cacheKey = `segment_data_${orgId}`;
		const cachedData = segmentCache.get(cacheKey);
		if (cachedData) {
			console.log('Returning cached data');
			return JSON.parse(cachedData);
		}

		// Retrieve organization segments for the specified orgId
		const organizationSegments = await this.organizationSegmentRepository.find({
			where: {
				orgId,
				// archived: false // Only return non-archived segments by default
			},
			include: [{
				relation: 'organizationSegmentDetails',
				scope: {
					include: [{relation: 'metricSegment'}],
				},
			}],
		});

		const enrichedSegments = [];

		for (const segment of organizationSegments) {
			const details = await this.organizationSegmentDetailsRepository.find({
				where: {orgSegmentId: segment.id},
				include: [{relation: 'metricSegment'}],
			});

			let profileCount = 0;
			let totalRevenue = 0;
			let avgLtv = 0;

			// for (const detail of details) {
			// 	const metricSegment = await this.metricSegmentRepository.findById(detail.metricSegmentId);

			// 	if (metricSegment.query && !metricSegment.integrationId) {
			// 		// Convert the metricSegment.query into a LoopBack query object
			// 		console.log("Metric Segment Query:", metricSegment.query);
			// 		const sqlWhere = this.buildSqlCondition(JSON.parse(metricSegment.query));
			// 		console.log("Converted LoopBack Query:", sqlWhere);

			// 		// Generate a raw SQL query to aggregate data
			// 		const sqlQuery = `
			// 		SELECT
			// 			COUNT(*) as "profileCount",
			// 			SUM("revenue") as "totalRevenue",
			// 			AVG("ltv") as "averageLtv"
			// 		FROM "public"."raleonuseridentity"
			// 		WHERE  OrgId = ${orgId} AND ${sqlWhere};
			// 	`;
			// 		console.log("Generated SQL Query:", sqlQuery);
			// 		// Execute the SQL query using the repository's DataSource
			// 		const [result] = await this.raleonUserIdentityRepository.dataSource.execute(sqlQuery);
			// 		profileCount += parseInt(result.profileCount, 10);
			// 		totalRevenue += parseFloat(result.totalRevenue || 0);
			// 		avgLtv = result.averageLtv ? parseFloat(result.averageLtv) : avgLtv;
			// 	}
			// }


			const data = {
				positive: details
					.filter(detail => detail.include && detail.metricSegment)
					.map(detail => ({
						...detail.metricSegment,
						query: detail.queryOverride || detail.metricSegment?.query
					})),
				negative: details
					.filter(detail => !detail.include && detail.metricSegment)
					.map(detail => ({
						...detail.metricSegment,
						query: detail.queryOverride || detail.metricSegment?.query
					})),
			};


			const combinedConditions = [];
			let hasIntegration = false;
			// Process positive queries
			for (const signal of data.positive) {
				if (signal!.integrationId) {
					hasIntegration = true;
					continue;
				}

				if (signal!.query && !signal!.integrationId) {
					try {
						const parsedQuery = JSON.parse(signal!.query);
						combinedConditions.push(parsedQuery);
					} catch (error) {
						console.error(
							`Error parsing positive MetricSegment query (ID: ${signal!.id}):`,
							error
						);
					}
				}
			}

			// Process negative queries
			for (const signal of data.negative) {
				if (signal!.integrationId) {
					hasIntegration = true;
					continue;
				}

				if (signal!.query && !signal!.integrationId) {
					try {
						const parsedQuery = JSON.parse(signal!.query);
						const flippedQuery = this.flipOperators(parsedQuery);
						combinedConditions.push(flippedQuery);
					} catch (error) {
						console.error(
							`Error parsing negative MetricSegment query (ID: ${signal!.id}):`,
							error
						);
					}
				}
			}

			// Combine all conditions into a single SQL WHERE clause
			const combinedQuery = {
				operator: 'and',
				conditions: [{field: 'orgId', operator: '=', value: orgId}, ...combinedConditions],
			};

			const sqlCondition = this.buildSqlCondition(combinedQuery);
			console.log('Generated SQL Condition:', sqlCondition);

			// Generate the SQL query
			const sqlQuery = `
			SELECT
				COUNT(*) as "profileCount",
				SUM("revenue") as "totalRevenue",
				AVG("ltv") as "averageLtv",
				(SUM(CASE WHEN "email_unique_opensl90" >= 1 THEN 1 ELSE 0 END)) AS "usersOpenedEmailL90"
			FROM "public"."raleonuseridentity"
			WHERE ${sqlCondition};
			`;
			console.log('Generated SQL Query:', sqlQuery);

			// Execute the SQL query
			const [result] = await this.raleonUserIdentityRepository.dataSource.execute(sqlQuery);
			profileCount += parseInt(result.profileCount, 10);
			totalRevenue += parseFloat(result.totalRevenue || 0);
			avgLtv = result.averageLtv ? parseFloat(result.averageLtv) : avgLtv;
			const usersOpenedEmailL90 = (parseInt(result.usersOpenedEmailL90, 0) / profileCount) * 100.0;
			// customers.push(...results);

			console.log(`Profile Count: ${profileCount}, Total Revenue: ${totalRevenue}, Avg LTV: ${avgLtv}`);

			enrichedSegments.push({
				...segment,
				tag: "AI Managed",
				profileCount,
				avgltv: Intl.NumberFormat('en-US', {
					style: 'currency',
					currency: 'USD',
					maximumSignificantDigits: 2,
					notation: 'compact',
				}).format(avgLtv),
				revenue: Intl.NumberFormat('en-US', {
					style: 'currency',
					currency: 'USD',
					maximumSignificantDigits: 3,
					notation: 'compact',
				}).format(totalRevenue),
				emailopenrate: `${usersOpenedEmailL90.toFixed(0)}%`,
				emailctr: "23%",
				sending: false,
			});
		}

		console.log("Fetched and Enriched Organization Segments:", enrichedSegments);
		if(enrichedSegments.length > 0) segmentCache.set(cacheKey, JSON.stringify(enrichedSegments));
		return enrichedSegments;
	}


	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@skipGuardCheck()
	@get('/organization-segment/{id}', {
		responses: {
			'200': {
				description: 'OrganizationSegment associated with the Organization',
				content: {
					'application/json': {
						schema: {},
					},
				},
			},
		},
	})
	async getSegmentById(
		@param.path.number('id') id: number,
		@param.query.number('page') page: number = 1,
		@injectUserOrgId() orgId: number,
		pageSize = 10,
		includeAllData = true,
		customers?: Array<any>,
		includeShopperData = true
	): Promise<any> {
		console.log("Injected orgId:", orgId);

		// Retrieve organization segments for the specified orgId
		const segment = await this.organizationSegmentRepository.findById(id, {
			include: [{
				relation: 'organizationSegmentDetails',
				scope: {
					include: [{relation: 'metricSegment'}],
				},
			}],
		});

		if (!segment) {
			throw new HttpErrors.NotFound('Organization segment not found');
		}

		if (segment.orgId != orgId) {
			throw new HttpErrors.Forbidden('You are not authorized to access this segment');
		}

		const details = await this.organizationSegmentDetailsRepository.find({
			where: {orgSegmentId: segment.id},
			include: [{relation: 'metricSegment'}],
		});

		let hasIntegration = false;
		customers = [];

		const data = {
			positive: details
				.filter(detail => detail.include && detail.metricSegment)
				.map(detail => ({
					...detail.metricSegment,
					query: detail.queryOverride || detail.metricSegment?.query
				})),
			negative: details
				.filter(detail => !detail.include && detail.metricSegment)
				.map(detail => ({
					...detail.metricSegment,
					query: detail.queryOverride || detail.metricSegment?.query
				})),
		};

		const combinedConditions = [];
		// Process positive queries
		for (const signal of data.positive) {
			if (signal!.integrationId) {
				hasIntegration = true;
				continue;
			}
			if (signal!.query && !signal!.integrationId) {
				try {
					const parsedQuery = JSON.parse(signal!.query);
					combinedConditions.push(parsedQuery);
				} catch (error) {
					console.error(
						`Error parsing positive MetricSegment query (ID: ${signal!.id}):`,
						error
					);
				}
			}
		}

		// Process negative queries
		for (const signal of data.negative) {
			if (signal!.integrationId) {
				hasIntegration = true;
				continue;
			}
			if (signal!.query && !signal!.integrationId) {
				try {
					const parsedQuery = JSON.parse(signal!.query);
					const flippedQuery = this.flipOperators(parsedQuery);
					combinedConditions.push(flippedQuery);
				} catch (error) {
					console.error(
						`Error parsing negative MetricSegment query (ID: ${signal!.id}):`,
						error
					);
				}
			}
		}

		// Combine all conditions into a single SQL WHERE clause
		const combinedQuery = {
			operator: 'and',
			conditions: [{field: 'orgId', operator: '=', value: orgId}, ...combinedConditions],
		};

		const sqlCondition = this.buildSqlCondition(combinedQuery);
		console.log('Generated SQL Condition:', sqlCondition);

		// First get aggregates
		const aggregateQuery = `
		SELECT
			COUNT(*) as "totalCount",
			SUM("revenue") as "totalRevenue",
			AVG("ltv") as "averageLtv",
			AVG("aov") as "averageAov",
			SUM("ltv") as "totalSpend",
			SUM("email_unique_opensl30") as "emailOpenUniquesL30",
			SUM("email_unique_opensl90") as "emailOpenUniquesL90",
			SUM("email_total_opensl30") as "emailOpenTotalL30",
			SUM("email_total_opensl90") as "emailOpenTotalL90"
		FROM "public"."raleonuseridentity"
		WHERE ${sqlCondition}
		`;

		const [aggregates] = await this.raleonUserIdentityRepository.dataSource.execute(aggregateQuery);

		// Then get paginated customer data
		const offset = (page - 1) * pageSize;
		const paginatedQuery = `
		SELECT
			"id",
			"identityvalue",
			"raleonuserid",
			"revenue",
			"ltv",
			"aov",
			"email_unique_opensl30",
			"email_unique_opensl90",
			"email_total_opensl30",
			"email_total_opensl90",
			"totalorders",
			"isdemouser"
		FROM "public"."raleonuseridentity"
		WHERE ${sqlCondition}
		LIMIT ${pageSize} OFFSET ${offset}
		`;

		const customerResults = await this.raleonUserIdentityRepository.dataSource.execute(paginatedQuery);
		customers = customerResults;

		let shopperInfo;
		let demoShopperInfo: any[] = [];
		if (includeShopperData) {
			if (customers && customers.some((customer: any) => customer.isdemouser)) {
				for (const customer of customers) {
					const firstName = faker.person.firstName();
					const lastName = faker.person.lastName();
					demoShopperInfo.push({
						id: customer.raleonuserid,
						shopify_id: customer.identityvalue,
						first_name: firstName,
						last_name: lastName,
						loyalty_score: 0,
						loyalty_tier: 'None',
						orders_count: customer.totalorders,
						total_spent: customer.ltv,
						loyalty_points: 0,
						email: `${firstName}.${lastName}@demo.com`,
						created_at: new Date(Math.floor(Math.random() * Date.now())).toLocaleDateString('en-US', {
							year: 'numeric',
							month: '2-digit',
							day: '2-digit'
						}),
						currency_balance_id: 0,
						currency_id: 0,
						points_log: [],
						rewards_log: [],
						birthday: new Date(Math.floor(Math.random() * Date.now())).toLocaleDateString('en-US', {
							year: 'numeric',
							month: '2-digit',
							day: '2-digit'
						}),
						...customer
					})
				}
			} else {
				const customerIds = customers ? customers.map((customer: any) => customer.identityvalue) : [];
				shopperInfo = await this.shopperInfo.getShopperInfo(orgId, 1, pageSize, undefined, customerIds);

				for (const shopper of (shopperInfo?.shoppers || [])) {
					shopper.customer = customers?.find((customer: any) => customer.id == shopper.id);
				}
			}
		}

		const klaviyo = await this.orgKeysRepository.findOne({
			where: {organizationId: orgId, key: KLAVIYO_INTEGRATION_KEY},
		});

		const totalPages = Math.ceil(aggregates.totalCount / pageSize);

		return {
			...segment,
			customers,
			shoppers: customers && customers.some((c: any) => c.isdemouser) ? demoShopperInfo : shopperInfo?.shoppers,
			aggregates: {
				totalCount: parseInt(aggregates.totalCount),
				totalRevenue: parseFloat(aggregates.totalRevenue || 0),
				averageLtv: parseFloat(aggregates.averageLtv || 0),
				averageAov: parseFloat(aggregates.averageAov || 0),
				totalSpend: parseFloat(aggregates.totalSpend || 0),
				emailOpenUniquesL30: parseInt(aggregates.emailOpenUniquesL30 || 0),
				emailOpenUniquesL90: parseInt(aggregates.emailOpenUniquesL90 || 0),
				emailOpenTotalL30: parseInt(aggregates.emailOpenTotalL30 || 0),
				emailOpenTotalL90: parseInt(aggregates.emailOpenTotalL90 || 0)
			},
			pagination: {
				currentPage: page,
				pageSize: pageSize,
				totalPages: totalPages,
				totalItems: parseInt(aggregates.totalCount)
			},
			isEspConfigured: klaviyo,
			hasIntegrationSignals: hasIntegration
		}
	}



	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@skipGuardCheck()
	@post('/generate-organization-segments', {
		responses: {
			'200': {
				description: 'Organization Segments created successfully',
				content: {'application/json': {schema: getModelSchemaRef(OrganizationSegment)}},
			},
		},
	})
	async generatePresetSegments(
		@injectUserOrgId() orgId: number,
	): Promise<void> {
		console.log("Generating preset segments for orgId:", orgId);
		// Get existing segments for this org
		const existingSegments = await this.organizationSegmentRepository.find({
			where: { orgId },
		});

		// Helper function to check if a segment exists
		const segmentExists = (name: string) => {
			return existingSegments.some(segment => segment.name === name);
		};

		// Helper function to create a segment if it doesn't exist
		const createSegmentIfNotExists = async (
			name: string,
			description: string,
			metricKey: string,
			additionalMetricKey?: string
		) => {
			if (!segmentExists(name)) {
				const segment = await this.organizationSegmentRepository.create({
					orgId,
					name,
					description,
				});

				const metric = await this.metricSegmentRepository.findOne({
					where: {segmentKey: metricKey},
				});

				await this.organizationSegmentDetailsRepository.create({
					orgSegmentId: segment.id,
					metricSegmentId: metric!.id,
					include: true,
				});

				if (additionalMetricKey) {
					const additionalMetric = await this.metricSegmentRepository.findOne({
						where: {segmentKey: additionalMetricKey},
					});

					await this.organizationSegmentDetailsRepository.create({
						orgSegmentId: segment.id,
						metricSegmentId: additionalMetric!.id,
						include: true,
					});
				}
			}
		};

		// 1. Site Engagement
		await createSegmentIfNotExists(
			'On-Site Engagement',
			'Customers who are visiting your store in the last 30 days. Scored by higher value pages, frequency, and other factors.',
			'site-engagement-30'
		);

		// 2. New Customers
		await createSegmentIfNotExists(
			'New Customers',
			'Customers who have made a single purchase recently.',
			'new-users'
		);

		// 3. Loyal Customers
		await createSegmentIfNotExists(
			'Loyal Customers',
			'Customers designated as Very Loyal',
			'very-loyal'
		);

		// 4. Upsell Opportunities
		await createSegmentIfNotExists(
			'Upsell Opportunities',
			"Customers that show potential to be loyal, but aren't quite there yet.",
			'upsell-opportunity'
		);

		// 5. Winback Customers
		await createSegmentIfNotExists(
			'Winback Customers',
			'Customers that were previously Very Loyal but recently show higher churn risk',
			'winback'
		);

		// 6. At Risk Engaged
		await createSegmentIfNotExists(
			'At Risk Engaged',
			"Customers that have engaged with your site recently, but still have a high probability of churning based on Raleon's churn prediction model.",
			'at-risk-engaged-customers'
		);

		// 7. High Value Customers
		await createSegmentIfNotExists(
			'High Value Customers',
			'Customers with the highest CLTV over the past 12 months.',
			'high-value-customers'
		);

		// 8. Ready to Buy Again
		await createSegmentIfNotExists(
			'Ready to Buy Again',
			'Customers approaching their typical repeat purchase window.',
			'likely-for-rebuy-soon'
		);

		// 9. Promo Responsive (formerly Discount Seekers)
		await createSegmentIfNotExists(
			'Promo Responsive',
			'Customers that primarily purchase with discounts.',
			'discounter-seekers'
		);

		// 10. Everyone (Email)
		await createSegmentIfNotExists(
			'Everyone',
			'These are all customers that have made at least one purchase within the last 12 months starting from the date Raleon is installed.',
			'everyone',
			'can-receive-email-marketing'
		);

		this.createKlayvioMetricSegments(orgId).catch((error) => {
			console.error('Error creating Klaviyo metric segments:', error);
		});
		console.log("DONE Generating preset segments for orgId:", orgId);
	}


	async createKlayvioMetricSegments(orgId: number): Promise<void> {
		const existingDetails = await this.organizationIntegrationDetailsRepository.findOne({
			where: {
				orgId,
				integrationId: 2
			}
		});
		if (existingDetails && existingDetails.connectedDate === 'scope-failure') {
			return;
		}

		try {
			// Check if Klaviyo integration is enabled
			const klaviyoKey = await this.orgKeysRepository.findOne({
				where: {organizationId: orgId, key: KLAVIYO_INTEGRATION_KEY},
			});

			if (!klaviyoKey) {
				return;
			}

			// Get all metric segments with Klaviyo integration ID
			const metricSegments = await this.metricSegmentRepository.find({
				where: {
					integrationId: 2 // Klaviyo integration ID
				}
			});

			// For each Klaviyo metric segment
			for (const segment of metricSegments) {
				try {
					if (!segment.query) continue;

					const parsedQuery = JSON.parse(segment.query);
					if (!parsedQuery.conditions?.[0]) continue;

					const condition = parsedQuery.conditions[0];
					const metricName = `Raleon Stats - ${segment.name}`;
					const operator = condition.operator;
					const value = parseInt(condition.value, 10);
					const days = condition.days || 30;

					// Check if segment already exists in Klaviyo
					const existingSegmentId = orgId ? await this.klaviyoService.checkSegmentExists(orgId, metricName) : null;

					if (!existingSegmentId) {
						// Create corresponding segment in Klaviyo with metric-based rule
						const data = {
							name: metricName, // Use metric name as segment name for consistency
							description: segment.description,
							positive: [segment], // Include the current metric segment as positive
							negative: [] // No negative segments
						};

						if (orgId) {
							await this.klaviyoService.createSegment(orgId, data);
						}
					} else {
						console.log(`Segment "${metricName}" already exists in Klaviyo with ID: ${existingSegmentId}`);
					}
				} catch (error) {
					console.error(`Error creating Klaviyo segment for ${segment.name}:`, error);
					if (error?.isScopeError) {
						try {
							const details = await this.organizationIntegrationDetailsRepository.findOne({
								where: {
									orgId,
									integrationId: 2
								}
							}) || await this.organizationIntegrationDetailsRepository.create({
								orgId,
								integrationId: 2
							});


							details.connectedDate = 'scope-failure';
							await this.organizationIntegrationDetailsRepository.updateById(details.id, details);
						} catch(error2) {
							console.error('Error updating Klaviyo integration details:', error2);
						}
					}
				}
			}
		} catch (error) {
			console.error('Error in createKlayvioMetricSegments:', error);
			throw error;
		}
	}


	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@skipGuardCheck()
	@post('/organization-segment', {
		responses: {
			'200': {
				description: 'Organization Segment created successfully',
				content: {
					'application/json': {
						schema: getModelSchemaRef(OrganizationSegment),
					},
				},
			},
		},
	})
	async createSegment(
		@requestBody({
			content: {
				'application/json': {
					schema: {
						type: 'object',
						properties: {
							name: {type: 'string'},
							description: {type: 'string'},
							positive: {type: 'array', items: {type: 'object'}},
							negative: {type: 'array', items: {type: 'object'}},
						},
					},
				},
			},
		})
		data: {
			name: string;
			description?: string;
			positive: any[];
			negative: any[];
		},
		@injectUserOrgId() orgId: number,
	): Promise<OrganizationSegment> {

		const cacheKey = `segment_data_${orgId}`;
		//clear cache
		segmentCache.del(cacheKey);


		// Create the segment in your database
		const segment = await this.organizationSegmentRepository.create({
			name: data.name,
			description: data.description,
			orgId: orgId,
		});

		// Save segment details
		for (const signal of data.positive) {
			await this.organizationSegmentDetailsRepository.create({
				metricSegmentId: signal.id,
				orgSegmentId: segment.id,
				include: true,
				queryOverride: signal.queryOverride,
			});
		}

		for (const signal of data.negative) {
			await this.organizationSegmentDetailsRepository.create({
				metricSegmentId: signal.id,
				orgSegmentId: segment.id,
				include: false,
				queryOverride: signal.queryOverride,
			});
		}

		return segment;
	}


	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@skipGuardCheck()
	@patch('/organization-segment/{id}', {
		responses: {
			'200': {
				description: 'Organization Segment created successfully',
				content: {
					'application/json': {
						schema: getModelSchemaRef(OrganizationSegment),
					},
				},
			},
		},
	})
  async updateSegment(
		@requestBody({
			content: {
				'application/json': {
					schema: {
						type: 'object',
						properties: {
							name: {type: 'string'},
							archived: {type: 'boolean'},
							archivedDate: {type: 'string', format: 'date-time'},
						},
					},
				},
			},
		})
		data: {
			name?: string;
			archived?: boolean;
			archivedDate?: string;
		},
		@param.path.number('id') id: number,
		@injectUserOrgId() orgId: number,
	): Promise<void> {
		const segment = await this.organizationSegmentRepository.findById(id);
		if (segment.orgId !== orgId) {
			throw new HttpErrors.Forbidden('You are not authorized to update this segment');
		}

		const updateData: any = {};
		if (data.name !== undefined) updateData.name = data.name;
		if (data.archived !== undefined) updateData.archived = data.archived;
		if (data.archivedDate !== undefined) updateData.archivedDate = data.archivedDate;

		// If segment is being archived and has a Klaviyo external ID, update the Klaviyo segment name
		if (data.archived === true && segment.externalId) {
			try {
				const segmentDetails = await this.organizationSegmentDetailsRepository.find({
					where: {orgSegmentId: segment.id},
					include: [{relation: 'metricSegment'}],
				});

				const klaviyoData = {
					name: `Archived - ${data.name || segment.name}`,
					description: segment.description,
					positive: segmentDetails
						.filter(detail => detail.include)
						.map(detail => ({
							...detail.metricSegment,
							query: detail.queryOverride || detail.metricSegment?.query
						})),
					negative: segmentDetails
						.filter(detail => !detail.include)
						.map(detail => ({
							...detail.metricSegment,
							query: detail.queryOverride || detail.metricSegment?.query
						}))
				};

				await this.klaviyoService.updateSegment(orgId, segment.externalId, klaviyoData);
			} catch (error) {
				console.error('Error updating Klaviyo segment name:', error);
				// Continue with local update even if Klaviyo update fails
			}
		}

		await this.organizationSegmentRepository.updateById(id, updateData);

		// // Save segment details
		// for (const signal of data.positive) {
		// 	await this.organizationSegmentDetailsRepository.create({
		// 		metricSegmentId: signal.id,
		// 		orgSegmentId: segment.id,
		// 		include: true,
		// 	});
		// }

		// for (const signal of data.negative) {
		// 	await this.organizationSegmentDetailsRepository.create({
		// 		metricSegmentId: signal.id,
		// 		orgSegmentId: segment.id,
		// 		include: false,
		// 	});
		// }

		// return segment;
	}

	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@skipGuardCheck()
	@get('/signals', {
		responses: {
			'200': {
				description: 'Array of MetricSegments marked as signals with dynamic size',
				content: {
					'application/json': {
						schema: {
							type: 'array',
							items: getModelSchemaRef(MetricSegment),
						},
					},
				},
			},
		},
	})
	async fetchSignals(@injectUserOrgId() orgId: number): Promise<any[]> {
		const defaultSignals = await this.metricSegmentRepository.find({
			where: {
				isSignal: true,
				isDefault: true,
			},
		});
		console.log("Default Signals:", orgId);
		const orgMetricSegments = await this.organizationMetricSegmentRepository.find({
			where: {
				orgId: orgId,
			},
			include: [{relation: 'metricSegment'}],
		});
		console.log("Org Metric Segments:", orgMetricSegments);
		const orgSignals = orgMetricSegments.map(oms => oms.metricSegment);

		return [...defaultSignals, ...orgSignals];
	}

	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@skipGuardCheck()
	@post('/organization-segment/calculate-size', {
		responses: {
			'200': {
				description:
					'Calculates the total size, sum of revenue, and average LTV based on the combined queries of positive and negative metric segments.',
				content: {
					'application/json': {
						schema: {
							type: 'object',
							properties: {
								totalSize: {type: 'number'},
								totalRevenue: {type: 'number'},
								averageLtv: {type: 'number'},
							},
						},
					},
				},
			},
		},
	})
	async calculateSize(
		@requestBody({
			content: {
				'application/json': {
					schema: {
						type: 'object',
						properties: {
							positive: {type: 'array', items: {type: 'object'}},
							negative: {type: 'array', items: {type: 'object'}},
						},
					},
				},
			},
		})
		data: {
			positive: any[];
			negative: any[];
		},
		@injectUserOrgId() orgId: number
	): Promise<{totalSize: number; totalRevenue: number; averageLtv: number}> {
		const combinedConditions: any[] = [];
		const klaviyoSignals: {signal: MetricSegment; include: boolean}[] = [];

		// Separate Klaviyo signals and SQL conditions
		for (const signal of data.positive) {
			if (signal.integrationId) {
				klaviyoSignals.push({signal, include: true});
			} else if (signal.query) {
				try {
					const parsedQuery = signal.queryOverride ? JSON.parse(signal.queryOverride) : JSON.parse(signal.query);
					combinedConditions.push(parsedQuery);
				} catch (error) {
					console.error(
						`Error parsing positive MetricSegment query (ID: ${signal.id}):`,
						error
					);
				}
			}
		}

		for (const signal of data.negative) {
			if (signal.integrationId) {
				klaviyoSignals.push({signal, include: false});
			} else if (signal.query) {
				try {
					const parsedQuery = signal.queryOverride ? JSON.parse(signal.queryOverride) : JSON.parse(signal.query);
					const flippedQuery = this.flipOperators(parsedQuery);
					combinedConditions.push(flippedQuery);
				} catch (error) {
					console.error(
						`Error parsing negative MetricSegment query (ID: ${signal.id}):`,
						error
					);
				}
			}
		}

		// Get Klaviyo customer IDs if there are any Klaviyo signals
		let klayvioPositiveSignalCount = 0;
		let klaviyoPositiveCustomerIds = new Set<string>();
		let klaviyoNegativeCustomerIds = new Set<string>();
		if (klaviyoSignals.length > 0) {
			try {
				for (const {signal, include} of klaviyoSignals) {
					if (!signal.query) continue;

					if (include) {
						klayvioPositiveSignalCount++;
					}

					try {
						const parsedQuery = JSON.parse(signal.query);
						if (!parsedQuery.conditions?.[0]) continue;

						const condition = parsedQuery.conditions[0];
						const metricName = `Raleon Stats - ${signal.name}`;
						const operator = include ? condition.operator : this.flipOperator(condition.operator);
						const value = parseInt(condition.value, 10);
						const days = condition.days || 30;

						const customerIds = await this.klaviyoService.getMetricCustomers(
							orgId,
							`Raleon Stats - ${metricName}`,
							operator,
							value,
							days
						);

						// For positive signals, add to the set
						// For negative signals, remove from the set
						if (include) {
							customerIds.forEach(id => klaviyoPositiveCustomerIds.add(id));
						} else {
							customerIds.forEach(id => klaviyoNegativeCustomerIds.add(id));
						}
					} catch (error) {
						console.error(
							`Error processing Klaviyo signal (ID: ${signal.id}):`,
							error
						);
					}
				}
			} catch (error) {
				console.error('Error getting Klaviyo metrics:', error);
				throw new HttpErrors.InternalServerError('Failed to get Klaviyo metrics');
			}
		}

		// Build SQL query including Klaviyo customer IDs
		let sqlCondition = '';
		if (combinedConditions.length > 0) {
			const combinedQuery = {
				operator: 'and',
				conditions: [{field: 'orgId', operator: '=', value: orgId}, ...combinedConditions],
			};
			sqlCondition = this.buildSqlCondition(combinedQuery);
		} else {
			sqlCondition = `orgId = ${orgId}`;
		}

		// Add Klaviyo customer IDs to SQL condition if any exist
		if (klaviyoNegativeCustomerIds.size > 0) {
			const customerIdList = Array.from(klaviyoNegativeCustomerIds).map(id => `'${id}'`).join(',');
			sqlCondition = `${sqlCondition} AND "identityvalue" NOT IN (${customerIdList})`;
		}

		if (klaviyoPositiveCustomerIds.size > 0 || klayvioPositiveSignalCount) {
			const customerIdList = Array.from(klaviyoPositiveCustomerIds).map(id => `'${id}'`).join(',');
			sqlCondition = `${sqlCondition} AND "identityvalue" IN (${customerIdList})`;
		}

		console.log('Generated SQL Condition:', sqlCondition);

		// Generate and execute the SQL query
		const sqlQuery = `
		  SELECT
			COUNT(*) as "totalSize",
			SUM("revenue") as "totalRevenue",
			AVG("ltv") as "averageLtv"
		  FROM "public"."raleonuseridentity"
		  WHERE ${sqlCondition};
		`;
		console.log('Generated SQL Query:', sqlQuery);

		const [result] = await this.raleonUserIdentityRepository.dataSource.execute(sqlQuery);

		// Parse and return the results
		const totalSize = parseInt(result.totalSize, 10);
		const totalRevenue = parseFloat(result.totalRevenue || 0);
		const averageLtv = parseFloat(result.averageLtv || 0);

		console.log('Calculation Results:', {
			totalSize,
			totalRevenue,
			averageLtv,
			klaviyoCustomerCount: (klaviyoPositiveCustomerIds.size + klaviyoNegativeCustomerIds.size),
		});

		return {
			totalSize,
			totalRevenue,
			averageLtv,
		};
	}


	/**
	 * Flips operators for a negative query.
	 * E.g., '=' becomes '!=', '<' becomes '>', etc.
	 */
	flipOperators(query: any): any {
		if (!query || !query.conditions) return query;

		query.conditions = query.conditions.map((condition: any) => {
			if (condition.operator === 'or' || condition.operator === 'and') {
				return this.flipOperators(condition); // Recursively flip nested conditions
			}
			if (condition.operator) {
				condition.operator = this.flipOperator(condition.operator);
			}
			return condition;
		});

		return query;
	}

	/**
	 * Maps operators to their flipped counterparts.
	 */
	flipOperator(operator: string): string {
		const operatorMapping: Record<string, string> = {
			'=': '!=',
			'!=': '=',
			'>': '<=',
			'>=': '<',
			'<': '>=',
			'<=': '>',
			'IS': 'IS NOT',
			'IS NOT': 'IS',
		};
		return operatorMapping[operator] || operator; // Default to same operator if not mapped
	}

	buildSqlCondition(query: any): string {
		if (!query || typeof query !== 'object') {
			throw new Error('Invalid query object');
		}

		// Track whether we need to join with attributes table
		let needsAttributesJoin = false;
		const attributeConditions: any[] = [];

		const processCondition = (condition: any): string => {
			if (!condition) return '1=1';

			if (condition.operator === 'or' || condition.operator === 'and') {
				// Process nested conditions
				const operator = condition.operator.toUpperCase();
				const subConditions = condition.conditions
					.map(processCondition)
					.filter(Boolean); // Remove invalid conditions
				return `(${subConditions.join(` ${operator} `)})`;
			}

			// If no operator, assume a single condition
			if (condition.conditions && Array.isArray(condition.conditions)) {
				// Default to AND if no operator is specified
				const subConditions = condition.conditions.map(processCondition).filter(Boolean);
				return `(${subConditions.join(' AND ')})`;
			}

			// Process individual condition
			const sqlOperator = this.getSqlOperator(condition.operator);

			if (condition.table === 'raleonuseridentityattributes') {
				// For custom attributes, track separately
				needsAttributesJoin = true;
				const field = condition.field;
				const value =
					typeof condition.value === 'string' && condition.value !== 'NULL'
						? `'${condition.value.replace(/'/g, "''")}'`
						: condition.value;

				attributeConditions.push({
					key: field,
					operator: sqlOperator,
					value: value
				});

				// Placeholder - actual condition will be added in final query construction
				return '1=1';
			} else {
				// Direct raleonuseridentity attribute
				const field = condition.field;
				const value =
					typeof condition.value === 'string' && condition.value !== 'NULL'
						? `'${condition.value.replace(/'/g, "''")}'`
						: condition.value;

				return `public.raleonuseridentity.${field} ${sqlOperator} ${value}`;
			}
		};

		// Process the main query
		const mainCondition = processCondition(query);

		// Construct the final query with optional join
		if (!needsAttributesJoin) {
			return mainCondition;
		}

		// Construct JOIN conditions for attributes
		const attributeJoinConditions = attributeConditions.map((attr, index) =>
			`rua${index}."key" = '${attr.key}' AND rua${index}.value ${attr.operator} ${attr.value}`
		).join(' AND ');

		// Construct the full query with multiple LEFT JOINs for each attribute condition
		return `
		  (${mainCondition}) AND
		  ${attributeConditions.map((_, index) =>
			`EXISTS (
			  SELECT 1
			  FROM public.raleonuseridentityattributes rua${index}
			  WHERE rua${index}.raleonuseridentityid = public.raleonuseridentity.id
			  AND ${attributeJoinConditions}
			)`
		).join(' AND ')}
		`;
	}


	getSqlOperator(operator: string): string {
		const operatorMapping: Record<string, string> = {
			'>': '>',
			'>=': '>=',
			'<': '<',
			'<=': '<=',
			'=': '=',
			'!=': '<>',
			'IS': 'IS',
			'IS NOT': 'IS NOT',
		};
		return operatorMapping[operator] || '=';
	}
}
