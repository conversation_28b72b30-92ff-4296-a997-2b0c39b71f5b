import {authenticate} from '@loopback/authentication';
import {authorize} from '@loopback/authorization';
import {
	Count,
	CountSchema,
	Filter,
	model,
	Model,
	property,
	repository,
	Where,
	WhereBuilder,
} from '@loopback/repository';
import {
	post,
	param,
	get,
	getModelSchemaRef,
	patch,
	del,
	requestBody,
	response,
	HttpErrors,
	api,
} from '@loopback/rest';
import {Invite, UserWithPassword} from '../models';
import {InviteRepository, UserRepository} from '../repositories';
import {basicAuthorization} from '../services/basic.authorizor';
import {uuid} from 'uuidv4';
import {User, UserServiceBindings} from '@loopback/authentication-jwt';
import {validateCredentials} from '../services/validator';
import {inject} from '@loopback/core';
import {UserManagementService} from '../services/user-management-service';
import {EmailService} from '../services/email.service';
import { SecurityBindings, UserProfile } from '@loopback/security';
import {injectGuardedFilter, OrgGuardPropertyStrategy, guardStrategy, CrudGuardInterceptor, restrictReadsWithGuard, skipGuardCheck, injectUserOrgId} from '../interceptors';

@model()
export class NewInviteRequest extends Model {
	@property({
		type: 'string',
		required: true,
	})
	adminEmail: string;

	@property({
		type: 'string',
		required: true,
	})
	email: string;

	@property({
		type: 'array',
		itemType: 'string',
		required: true
	})
	roles: string[];
}

@model()
export class ResendInviteRequest extends Model {
	@property({
		type: 'string',
		required: true,
	})
	adminEmail: string;

	@property({
		type: 'string',
		required: true,
	})
	email: string;
}

@model()
export class AcceptInviteRequest extends Model {
	@property({
		type: 'string',
		required: true,
	})
	email: string;

	@property({
		type: 'string',
		required: true,
	})
	password: string;

	@property({
		type: 'string',
		required: true,
	})
	inviteCode: string;

	@property({
		type: 'string',
		required: true,
	})
	firstName: string;

	@property({
		type: 'string',
		required: true,
	})
	lastName: string;
}

@api({basePath: '/api/v1'})
@guardStrategy(new OrgGuardPropertyStrategy<any>({
	orgIdModelPropertyName: 'organizationId',
	repositoryClass: UserRepository
}))
export class InviteController {
	constructor(
		@repository(InviteRepository)
		private inviteRepository : InviteRepository,
		@inject(SecurityBindings.USER, {optional: true}) private user: User,
		@repository(UserRepository)
		private userRepository : UserRepository,
		@inject(UserServiceBindings.USER_SERVICE)
		private userManagementService: UserManagementService,
		@inject('services.EmailService')
    	private emailService: EmailService,
	) {}

	@get('/admin/org-users/')
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'customer-admin'],
		voters: [basicAuthorization],
	})
	@skipGuardCheck()
	async getUsersAndInvites(
		@injectUserOrgId() orgId: number
	): Promise<any> {

		const users = await this.userRepository.find({
			fields: {
				id: true,
				email: true,
				firstName: true,
				lastName: true,
			},
			where: {
				organizationId: orgId,
			}
		});

		const unacceptedInvites = await this.inviteRepository.find({
			where: {
				orgId: orgId,
				accepted: false
			}
		});

		let pendingInvites: Invite[] = [];
		let expiredInvites: Invite[] = [];

		for (let i = 0; i < unacceptedInvites.length; i++) {
			if (new Date(unacceptedInvites[i].expirationDate!) < new Date()) {
				expiredInvites.push(unacceptedInvites[i]);
			} else {
				pendingInvites.push(unacceptedInvites[i]);
			}
		}

		return { users, pendingInvites, expiredInvites };

	}

	@post('/admin/invite-users')
	@response(200, {
		description: 'Invite model instance',
		content: {'application/json': {schema: getModelSchemaRef(Invite)}},
	})
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'customer-admin'],
		voters: [basicAuthorization],
	})
	@skipGuardCheck()
	async create(
		@requestBody.array(getModelSchemaRef(NewInviteRequest)) invites: NewInviteRequest[],
		@injectUserOrgId() orgId: number
	): Promise<Invite[]> {
		const filter: Filter = {};
		filter.where = filter.where || {};
		(filter.where as any).email = invites[0].adminEmail;
		(filter.where as any).organizationId = orgId;
		const adminUser = await this.userRepository.findOne({"where": filter.where});
		if (!adminUser) {
			throw new HttpErrors.NotFound('Administrator not found');
		}

		//find all users in this org and throw error if any of the emails already exist
		//also error if any emails have a pending invite
		const users = await this.userRepository.find({where: {organizationId: orgId}});
		const pendingInvites: string[] = [];
		invites.forEach(invite => {
			if (users.find(user => user.email === invite.email)) {
				throw new HttpErrors.BadRequest('User already exists in this organization');
			}
			pendingInvites.push(invite.email);
		});

		const existingInvites = await this.inviteRepository.find({where: {email: {inq: pendingInvites}, orgId }});
		if (existingInvites.length > 0) {
			throw new HttpErrors.BadRequest('User already has a pending invite');
		}

		const rolesToValidate: string[] = [];

		const newInvites = invites.map((invite: NewInviteRequest) => {
			invite.roles.forEach(role => {
				if (!rolesToValidate.includes(role)) {
					rolesToValidate.push(role);
				}
			});

			return new Invite({
				email: invite.email,
				orgId,
				dateSent: new Date().toISOString(),
				expirationDate: this.getExpirationDate(),
				accepted: false,
				inviteCode: uuid(),
				roles: invite.roles,
			});
		});

		this.validateRoles(rolesToValidate);

		const createdInvites = await this.inviteRepository.createAll(newInvites);


		for (const invite of createdInvites) {
			const existingUser = await this.userRepository.findOne({
				where: { email: invite.email }
			});
			await this.emailService.sendNewUserEmail(invite.email, invite.inviteCode!, !!existingUser);
		};

		return createdInvites;
	}

	@post('/admin/resend-invite')
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'customer-admin'],
		voters: [basicAuthorization],
	})
	@skipGuardCheck()
	async resendInvite(
		@requestBody({
			content: {
				'application/json': {
					schema: getModelSchemaRef(ResendInviteRequest),
				},
			},
		}) invite: ResendInviteRequest,
		@injectUserOrgId() userOrgId: number
	): Promise<any> {
		const adminUser = await this.userRepository.findOne({where: {
			email: invite.adminEmail,
			organizationId: userOrgId
		}});
		if (!adminUser) {
			throw new HttpErrors.NotFound('Administrator not found');
		}

		const invites = await this.inviteRepository.find({
			where: {
				orgId: userOrgId,
				email: invite.email,
				accepted: false
			}
		});

		if (invites.length === 0) {
			throw new HttpErrors.NotFound('This user does not have a pending invite');
		}

		const inviteToResend = invites[0];
		const newInviteCode = uuid();
		await this.inviteRepository.updateById(inviteToResend.id, {
			inviteCode: newInviteCode,
			dateSent: new Date().toISOString(),
			expirationDate: this.getExpirationDate(),
		});

		await this.emailService.sendNewUserEmail(inviteToResend.email, newInviteCode);
		return {
			statusCode: 200,
			body: 'Invite resent'
		}
	}

	@post('/organization/accept-invite')
	@response(200, {
		description: 'Invite model instance',
		content: {'application/json': {schema: getModelSchemaRef(Invite)}},
	})
	@skipGuardCheck()
	async accept(
		@requestBody(AcceptInviteRequest) invite: AcceptInviteRequest,
	): Promise<any> {
		const validatedInvite = await this.inviteRepository.find({
			where: {
				email: invite.email,
				inviteCode: invite.inviteCode,
			}
		});

		if (validatedInvite.length === 0) {
			throw new HttpErrors.NotFound('Invalid invite code. Please check your email for the latest invite.');
		}

		this.validateExpirationDate(validatedInvite[0].expirationDate!);

		const existingUser = await this.userRepository.findOne({
			where: { email: invite.email }
		});

		if (existingUser) {
			try {
				await this.userRepository.create({
					email: invite.email,
					organizationId: validatedInvite[0].orgId,
					firstName: existingUser.firstName,
					lastName: existingUser.lastName,
					roles: validatedInvite[0].roles,
					isSecondaryAccount: true
				});
			} catch (e) {
				throw new HttpErrors.BadRequest(e);
			}
		} else {
			validateCredentials({email: invite.email, password: invite.password});

			try {
				await this.userManagementService.createNewUser(new UserWithPassword({
					email: invite.email,
					password: invite.password,
					organizationId: validatedInvite[0].orgId,
					firstName: invite.firstName,
					lastName: invite.lastName,
					roles: validatedInvite[0].roles
				}));
			} catch (e) {
				throw new HttpErrors.BadRequest(e);
			}
		}

		validatedInvite[0].accepted = true;
		validatedInvite[0].inviteCode = '';

		await this.inviteRepository.updateById(validatedInvite[0].id!, validatedInvite[0]);
		return {
			statusCode: 200,
			body: {}
		}
	}

	@get('/organization/invite/{inviteCode}')
	@skipGuardCheck()
	@response(200, {
		description: 'Invite model instance',
		content: {
			'application/json': {
				schema: {
					type: 'object',
					items: getModelSchemaRef(Invite, {includeRelations: true}),
				},
			},
		},
	})
	async getInvite(
		@param.path.string('inviteCode') inviteCode: string
	): Promise<Invite[]> {
		return this.inviteRepository.find({where: {inviteCode}});
	}

	private getExpirationDate(): string {
		return new Date(new Date().getTime() + 1000 * 60 * 60 * 24 * 14).toISOString();
	}

	private validateExpirationDate(expirationDate: string): void {
		let today = new Date().getTime();
		let expiration = new Date(expirationDate).getTime();

		if (expiration < today) {
			throw new HttpErrors.BadRequest('Your invite has expired. Please request a new invite from your administrator.');
		}
	}

	private validateRoles(roles: string[]): void {
		roles.forEach(role => {
			if (!this.validRoles.includes(role)) {
				throw new HttpErrors.BadRequest('Invalid role specified');
			}
		});
	}

	@del('/admin/remove-user/{userId}')
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'customer-admin'],
		voters: [basicAuthorization],
	})
	@skipGuardCheck()
	async removeUser(
		@param.path.number('userId') userId: number,
		@inject(SecurityBindings.USER) currentUserProfile: UserProfile
	): Promise<any> {
		// Get the current user to find their organization
		const currentUser = await this.userRepository.findById(currentUserProfile.id);
		if (!currentUser) {
			throw new HttpErrors.NotFound('Current user not found');
		}
		const orgId = currentUser.organizationId;

		// Prevent self-removal
		if (parseInt(currentUserProfile.id) === userId) {
			throw new HttpErrors.BadRequest('You cannot remove yourself from the organization');
		}

		// Find the user to be removed
		const userToRemove = await this.userRepository.findById(userId);
		if (!userToRemove) {
			throw new HttpErrors.NotFound('User not found');
		}

		// Verify the user belongs to the same organization
		if (userToRemove.organizationId !== orgId) {
			throw new HttpErrors.Forbidden('User does not belong to your organization');
		}

		// Check if user has multiple accounts (secondary accounts)
		const userAccounts = await this.userRepository.find({
			where: {
				email: userToRemove.email
			}
		});

		// If user has multiple accounts, only delete the one in this org
		if (userAccounts.length > 1) {
			await this.userRepository.deleteById(userId);
		} else {
			// If this is the user's only account, delete the user and their credentials
			await this.userRepository.userCredentials(userId).delete();
			await this.userRepository.deleteById(userId);
		}

		return {
			statusCode: 200,
			message: 'User removed successfully'
		};
	}

	private validRoles = ['customer-admin', 'customer', 'support']
}
