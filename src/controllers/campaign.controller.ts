import {
	Filter,
	repository,
} from '@loopback/repository';
import {
	post,
	param,
	get,
	getModelSchemaRef,
	patch,
	put,
	del,
	requestBody,
	response,
	api,
	HttpErrors,
} from '@loopback/rest';
import {Campaign, CampaignMetricsContainer, Content, Goal, Reward} from '../models';
import {CampaignRepository, CampaignSegmentRepository, ContentRepository, GoalRepository, QuestRepository, RaleonUserIdentityRepository, RewardRepository, SegmentRepository, UserRepository} from '../repositories';
import {SecurityBindings} from '@loopback/security';
import {inject, service} from '@loopback/core';
import {User} from '@loopback/authentication-jwt';
import {authenticate} from '@loopback/authentication';
import {authorize} from '@loopback/authorization';
import {basicAuthorization} from '../services/basic.authorizor';
import {CampaignMetricsService} from '../services';
import {injectGuardedFilter, injectUserOrgId, guardStrategy} from '../interceptors/crud-guard.interceptor';
import {OrgGuardPropertyStrategy} from '../interceptors/crud-guard.interceptor';
import {restrictReadsWithGuard} from '../interceptors/crud-guard.interceptor';
import {skipGuardCheck} from '../interceptors/crud-guard.interceptor';
import {modelForGuard} from '../interceptors/crud-guard.interceptor';
import {modelIdForGuard} from '../interceptors/crud-guard.interceptor';

@api({basePath: '/api/v1'})
@guardStrategy(new OrgGuardPropertyStrategy<Campaign>({
	orgIdModelPropertyName: 'orgId',
	repositoryClass: CampaignRepository
}))
export class CampaignController {
	constructor(
		@inject(SecurityBindings.USER, {optional: true}) private user: User,
		@repository(UserRepository)
		private userRepository: UserRepository,
		@repository(CampaignRepository)
		public campaignRepository: CampaignRepository,
		@repository(CampaignSegmentRepository)
		public campaignSegmentRepository: CampaignSegmentRepository,
		@repository(QuestRepository)
		public questRepository: QuestRepository,
		@repository(ContentRepository)
		public contentRepository: ContentRepository,
		@repository(GoalRepository)
		private goalRepository: GoalRepository,
		@repository(RewardRepository)
		private rewardRepository: RewardRepository,
		@repository(SegmentRepository)
		public segmentRepository: SegmentRepository,
		@service(CampaignMetricsService)
		private campaignMetricsService: CampaignMetricsService,
		@repository(RaleonUserIdentityRepository)
		private raleonUserIdentityRepository: RaleonUserIdentityRepository,
	) { }

	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@post('/campaigns/{segmentId}')
	@response(200, {
		description: 'Campaign model instance',
		content: {'application/json': {schema: getModelSchemaRef(Campaign)}},
	})
	async create(
		@requestBody({
			content: {
				'application/json': {
					schema: getModelSchemaRef(Campaign, {
						title: 'NewCampaign',
						exclude: ['id', 'orgId', 'status'],
						includeRelations: true,
					}),
				},
			},
		})
		@modelForGuard(Campaign)
		campaign: Partial<Campaign>,
		@param.path.number('segmentId') segmentId: number,
		@injectUserOrgId() orgId: number,
		@param.query.string('klaviyoSegmentId') klaviyoSegmentId?: string,
		@param.query.string('segmentType') segmentType?: 'raleon' | 'klaviyo',
	): Promise<any> {
		const quests = campaign.quests!;
		delete campaign.quests;

		if (!campaign.orgId) {
			campaign.orgId = orgId;
		}

		// Validate segment based on type
		if (segmentType === 'klaviyo') {
			if (!klaviyoSegmentId) {
				throw new HttpErrors.BadRequest('Klaviyo segment ID is required when segment type is klaviyo');
			}
			// For Klaviyo segments, we don't validate against our segment repository
		} else {
			// Default to Raleon segment validation
			if (segmentId !== -1) {
				const segments = await this.segmentRepository.find({where: {id: segmentId, orgid: orgId}})
				if (!segments.length) {
					throw new HttpErrors.NotFound('Segment not found');
				}
			}
		}

		const newCampaign = await this.campaignRepository.create(campaign);

		// Create campaign-segment relationship based on type
		if (segmentType === 'klaviyo') {
			// For Klaviyo segments, create a campaign-segment record with the Klaviyo segment ID
			await this.campaignSegmentRepository.create({
				campaignId: newCampaign.id!,
				segmentId: -1, // Use -1 as placeholder for Klaviyo segments
				klaviyoSegmentId: klaviyoSegmentId,
				segmentType: 'klaviyo'
			});
		} else {
			// For Raleon segments, use the existing link method
			await this.campaignRepository.segments(newCampaign.id).link(segmentId);
		}
		const newQuest = await this.campaignRepository.quests(newCampaign.id).create({
			name: campaign.name,
		});

		const campaignNoContent =
			!quests || !quests.length || !quests![0].content ||
			!quests![0].content!.header &&
			!quests![0].content!.message &&
			!quests![0].content!.closeMessage &&
			!quests![0].content!.buttonText &&
			!quests![0].content!.buttonUrl;

		if (!campaignNoContent) {
			await this.createContent(newQuest.id!, quests![0].content!);
		}
		if (quests![0].goals && quests![0].goals!.length) {
			await this.createGoals(newQuest.id!, quests![0].goals!);
		}
		if (quests![0].rewards && quests![0].rewards!.length) {
			await this.createRewards(newQuest.id!, quests![0].rewards!);
		}

		return await this.findById(newCampaign.id);
	}

	private async createContent(questId: number, content: Partial<Content>) {
		if (!questId || !content) {
			return;
		}

		let contentCss;
		if (!content.css || !Object.keys(content.css).length) {
			contentCss = this.defaultContentCss;
		} else {
			contentCss = this.updateCssPositioning(content.css);
		}

		await this.questRepository.content(questId).create({
			questId: questId,
			header: content.header,
			headerImageUrl: content.headerImageUrl,
			message: content.message,
			closeMessage: content.closeMessage,
			buttonText: content.buttonText,
			buttonUrl: content.buttonUrl,
			css: contentCss,
		});
	}

	private async createGoals(questId: number, goals: Partial<Goal>[]) {
		if (!questId || !goals || !goals.length) {
			return;
		}

		for (const goal of goals) {
			const goalContent = goal?.content;
			delete goal.content;
			const newGoal = await this.questRepository.goals(questId).create({
				questId: questId,
				name: goal.name,
				type: goal.type,
				requiredData: goal.requiredData,
				externalId: goal.externalId,
			});

			if (goalContent) {
				await this.createGoalContent(newGoal.id!, goalContent);
			}
		}
	}

	private async createGoalContent(goalId: number, content: Partial<Content>) {
		if (!goalId || !content) {
			throw new HttpErrors.BadRequest('Goal content is missing');
		}

		await this.goalRepository.content(goalId).create({
			goalId: goalId,
			message: content.message,
			buttonText: content.buttonText,
			buttonUrl: content.buttonUrl,
			header: content.header
		});
	}

	private async createRewards(questId: number, rewards: Partial<Reward>[]) {
		if (!questId || !rewards || !rewards.length) {
			return;
		}

		for (const reward of rewards) {
			const rewardContent = reward?.content;
			delete reward.content;
			const newReward = await this.questRepository.rewards(questId).create({
				questId: questId,
				type: reward.type,
				configData: reward.configData,
			});

			if (rewardContent) {
				await this.createRewardContent(newReward.id!, rewardContent);
			}
		}
	}
	private async createRewardContent(rewardId: number, content: Partial<Content>) {
		if (!rewardId || !content) {
			throw new HttpErrors.BadRequest('Reward content is missing');
		}

		await this.rewardRepository.content(rewardId).create({
			rewardId: rewardId,
			message: content.message,
			buttonText: content.buttonText,
			buttonUrl: content.buttonUrl,
		});
	}

	@get('/campaigns')
	@response(200, {
		description: 'Array of Campaign model instances',
		content: {
			'application/json': {
				schema: {
					type: 'array',
					items: getModelSchemaRef(Campaign, {includeRelations: true}),
				},
			},
		},
	})
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@restrictReadsWithGuard({
		plural: true,
		filterOnly: true
	})
	async find(
		@param.filter(Campaign)
		@injectGuardedFilter()
		filter?: Filter<Campaign>,
	): Promise<Campaign[]> {
		return await this.campaignRepository.find(filter);
	}


	@get('/campaigns/metrics')
	@response(200, {
		description: 'Campaigns Metrics',
		content: {
			'application/json': {
				schema: {
					type: 'array',
					items: getModelSchemaRef(CampaignMetricsContainer),
				}
			},
		},
	})
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@skipGuardCheck()
	async getMetricsForCampaigns(
		@injectUserOrgId() orgId: number
	): Promise<object> {
		const campaignIds = await this.campaignRepository.find({where: {orgId}}).then(campaigns => campaigns.map(campaign => campaign.id).filter(x => x) as Array<number>);

		return this.campaignMetricsService.getMetricsForCampaignsEventStream(campaignIds);
	}


	@get('/campaigns/{id}')
	@response(200, {
		description: 'Campaign model instance',
		content: {
			'application/json': {
				schema: getModelSchemaRef(Campaign, {includeRelations: true}),
			},
		},
	})
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer', 'customer-admin', 'raleon-support'],
		voters: [basicAuthorization],
	})
	@restrictReadsWithGuard({
		plural: false,
	})
	async findById(
		@param.path.number('id')
		@modelIdForGuard(Campaign)
		id: typeof Campaign.prototype.id
	): Promise<Campaign | null> {
		return this.campaignRepository.findOne({
			where: {
				id: id,
			},
			include: [
				{relation: 'segments'},
				{
					relation: 'quests',
					scope: {
						include: [
							{relation: 'content'},
							{relation: 'goals', scope: {include: [{relation: 'content'}]}},
							{relation: 'rewards', scope: {include: [{relation: 'content'}]}},
						]
					},
				}
			],
		});
	}

	@get('/campaigns/{id}/metrics')
	@response(200, {
		description: 'Campaign Metrics',
		content: {
			'application/json': {
				schema: getModelSchemaRef(CampaignMetricsContainer)
			},
		},
	})
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	async getMetricsForCampaign(
		@param.path.number('id')
		@modelIdForGuard(Campaign)
		id: typeof Campaign.prototype.id,
	): Promise<Array<CampaignMetricsContainer>> {
		const campaign = await this.campaignRepository.findById(id);
		let metrics;
		if (campaign.type == 'NativeQuest') {
			metrics = await this.campaignMetricsService.getMetricsForNativeQuest(id!);
		} else {
			metrics = await this.campaignMetricsService.getMetricsForActionPrompt(id!);
		}

		return metrics;
	}

	@patch('/campaigns/{id}')
	@response(204, {
		description: 'Campaign PATCH success',
	})
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	async updateById(
		@param.path.number('id')
		@modelIdForGuard(Campaign)
		id: number,
		@requestBody({
			content: {
				'application/json': {
					schema: getModelSchemaRef(Campaign, {partial: true}),
				},
			},
		})
		@modelForGuard(Campaign)
		campaign: Partial<Campaign>,
	): Promise<Campaign | null> {
		await this.userRepository.findOrganization(this.user); // check if user is valid
		if (campaign.segments && campaign.segments.length > 0) {
			await this.campaignRepository.segments(id).unlinkAll();
			await this.campaignRepository.segments(id).link(campaign.segments[0].id);
			delete campaign.segments;
		}

		let quest;
		if (campaign.quests && campaign.quests.length > 0) {
			quest = campaign.quests[0];
			if (quest.content) {
				if (!quest.content.questId) {
					quest.content.questId = quest.id;
				}
				quest.content.css = this.updateCssPositioning(quest.content.css);
				await this.questRepository.content(quest.id).patch(quest.content);
			}

			if (quest.goals && quest.goals.length) {
				for (const goal of quest.goals) {
					if (!goal.id) {
						await this.createGoals(quest?.id!, [goal]);
						continue;
					}
					if (goal.content) {
						await this.goalRepository.content(goal.id).patch(goal.content);
					}
					delete goal.content;
					if (!goal.questId) {
						goal.questId = quest.id!;
					}
					await this.questRepository.goals(quest.id).patch(goal, {
						id: goal.id,
						externalId: goal.externalId,
					});
				}
			}

			if (quest.rewards && quest.rewards.length) {
				for (const reward of quest.rewards) {
					delete reward.content;
					if (!reward.questId) {
						reward.questId = quest.id!;
					}
					await this.questRepository.rewards(quest.id).patch(reward);
				}
			}
			delete campaign.quests;
		}
		await this.campaignRepository.updateById(id, campaign);
		return await this.findById(id);
	}

	@put('/campaigns/{id}')
	@response(204, {
		description: 'Campaign PUT success',
	})
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer', 'raleon-support'],
		voters: [basicAuthorization],
	})
	async replaceById(
		@param.path.number('id')
		@modelIdForGuard(Campaign)
		id: typeof Campaign.prototype.id,

		@modelForGuard(Campaign)
		@requestBody()
		campaign: Campaign,
	): Promise<void> {
		await this.campaignRepository.replaceById(id, campaign);
	}

	@del('/campaigns/{id}')
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@response(204, {
		description: 'Campaign DELETE success',
	})
	async deleteById(
		@param.path.number('id')
		@modelIdForGuard(Campaign)
		id: typeof Campaign.prototype.id
	): Promise<void> {
		const campaign = await this.campaignRepository.findById(id);
		if (campaign.status !== 'Draft') {
			throw new HttpErrors.BadRequest('You can only update campaigns in Draft status');
		}

		await this.campaignRepository.cascadeDeleteCampaign(id!);
	}

	@skipGuardCheck()
	@post('/campaigns/discord-info')
	@response(200, {
		description: 'Campaign PUT success',
	})
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer', 'raleon-support'],
		voters: [basicAuthorization],
	})
	async getWalletDiscordInfo(
		@requestBody()
		wallets: string[],
	): Promise<any[]> {

		const addresses = await this.raleonUserIdentityRepository.find({
			where: {
			  identityType: 'address',
			  identityValue: {
				inq: wallets,
			  },
			},
		  });

		const discordUsernames = await this.raleonUserIdentityRepository.find({
			where: {
			  identityType: {
				inq: ['discord_username', 'discord_global_name', 'discord_id'],
			  },
			},
		  });

		const discordUsernameMap = new Map();
		discordUsernames.forEach(record => {
			const existingRecord = discordUsernameMap.get(record.raleonUserId) || {};
			existingRecord[record.identityType] = record.identityValue;
			discordUsernameMap.set(record.raleonUserId, existingRecord);
		});

		const result = addresses
			.filter(record => discordUsernameMap.has(record.raleonUserId))
			.map(record => ({
				address: record.identityValue,
				...discordUsernameMap.get(record.raleonUserId),
		}));

		return result;
	}

	private updateCssPositioning(contentCss: any): any {
		contentCss.container['bottom'] = '1em';
		contentCss.container['left'] = '3em';
		contentCss.container['position'] = 'fixed';
		return contentCss;
	}

	defaultContentCss: any = {
		"container": {
			"display": "flex",
			"flex-direction": "column",
			"font-family": "Trebuchet MS",
			"align-items": "center",
			"gap": "0.5rem",
			"border-radius": "30px",
			"padding": "16px",
			"width": "fit-content",
			"max-width": "380px",
			"height": "fit-content",
			"maxHeight": "300px",
			"z-index": "999",
			"position": "fixed",
			"bottom": "1em",
			"left": "3em",
			"background-color": "rgb(30,19,50)",
			"background-image": "radial-gradient(rgba(255, 255, 255, 0.05) 10%, transparent 20%)",
			"background-position": "0 0, 50px 50px",
			"background-size": "20px 20px"
		},
		"header": {
			"color": "#FFF",
			"font-size": "18px",
			"font-weight": "600"
		},
		"headerImage": {
			"text-align": "center",
			"color": "#FFF",
			"margin-bottom": "0",
			"font-size": "18px"
		},
		"message": {
			"text-align": "center",
			"font-size": "15px",
			"color": "#FFF",
			"font-weight": "400",
			"margin-bottom": "0.5em"
		},
		"button": {
			"min-width": "100%",
			"color": "white",
			"font-size": "18px",
			"border-radius": "0.3em",
			"border": "none",
			"height": "48px",
			"background": "linear-gradient(261.82deg, rgb(90, 22, 201) -5.43%, rgb(42, 38, 63) 109.81%)",
			"cursor": "pointer"
		},
		"closeMessage": {
			"color": "#554EC3",
			"font-size": "14px",
			"cursor": "pointer",
			"text-align": "center"
		}
	}
}
